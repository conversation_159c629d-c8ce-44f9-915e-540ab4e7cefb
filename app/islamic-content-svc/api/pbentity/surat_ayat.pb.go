// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/surat_ayat.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SuratAyat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                       //
	AyatId        int32                  `protobuf:"varint,2,opt,name=AyatId,proto3" json:"AyatId,omitempty" dc:"经文全局ID"`   // 经文全局ID
	SurahId       int32                  `protobuf:"varint,3,opt,name=SurahId,proto3" json:"SurahId,omitempty" dc:"所属章节ID"` // 所属章节ID
	Nomor         int32                  `protobuf:"varint,4,opt,name=Nomor,proto3" json:"Nomor,omitempty" dc:"经文在章节中的编号"`  // 经文在章节中的编号
	Ar            string                 `protobuf:"bytes,5,opt,name=Ar,proto3" json:"Ar,omitempty" dc:"阿拉伯语经文"`            // 阿拉伯语经文
	Tr            string                 `protobuf:"bytes,6,opt,name=Tr,proto3" json:"Tr,omitempty" dc:"音译文本"`              // 音译文本
	Idn           string                 `protobuf:"bytes,7,opt,name=Idn,proto3" json:"Idn,omitempty" dc:"印尼语翻译"`           // 印尼语翻译
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty"`          //
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty"`          //
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuratAyat) Reset() {
	*x = SuratAyat{}
	mi := &file_pbentity_surat_ayat_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuratAyat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuratAyat) ProtoMessage() {}

func (x *SuratAyat) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_surat_ayat_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuratAyat.ProtoReflect.Descriptor instead.
func (*SuratAyat) Descriptor() ([]byte, []int) {
	return file_pbentity_surat_ayat_proto_rawDescGZIP(), []int{0}
}

func (x *SuratAyat) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SuratAyat) GetAyatId() int32 {
	if x != nil {
		return x.AyatId
	}
	return 0
}

func (x *SuratAyat) GetSurahId() int32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *SuratAyat) GetNomor() int32 {
	if x != nil {
		return x.Nomor
	}
	return 0
}

func (x *SuratAyat) GetAr() string {
	if x != nil {
		return x.Ar
	}
	return ""
}

func (x *SuratAyat) GetTr() string {
	if x != nil {
		return x.Tr
	}
	return ""
}

func (x *SuratAyat) GetIdn() string {
	if x != nil {
		return x.Idn
	}
	return ""
}

func (x *SuratAyat) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SuratAyat) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_pbentity_surat_ayat_proto protoreflect.FileDescriptor

const file_pbentity_surat_ayat_proto_rawDesc = "" +
	"\n" +
	"\x19pbentity/surat_ayat.proto\x12\bpbentity\x1a\x1fgoogle/protobuf/timestamp.proto\"\x89\x02\n" +
	"\tSuratAyat\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x16\n" +
	"\x06AyatId\x18\x02 \x01(\x05R\x06AyatId\x12\x18\n" +
	"\aSurahId\x18\x03 \x01(\x05R\aSurahId\x12\x14\n" +
	"\x05Nomor\x18\x04 \x01(\x05R\x05Nomor\x12\x0e\n" +
	"\x02Ar\x18\x05 \x01(\tR\x02Ar\x12\x0e\n" +
	"\x02Tr\x18\x06 \x01(\tR\x02Tr\x12\x10\n" +
	"\x03Idn\x18\a \x01(\tR\x03Idn\x128\n" +
	"\tCreatedAt\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tCreatedAt\x128\n" +
	"\tUpdatedAt\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tUpdatedAtB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_surat_ayat_proto_rawDescOnce sync.Once
	file_pbentity_surat_ayat_proto_rawDescData []byte
)

func file_pbentity_surat_ayat_proto_rawDescGZIP() []byte {
	file_pbentity_surat_ayat_proto_rawDescOnce.Do(func() {
		file_pbentity_surat_ayat_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_surat_ayat_proto_rawDesc), len(file_pbentity_surat_ayat_proto_rawDesc)))
	})
	return file_pbentity_surat_ayat_proto_rawDescData
}

var file_pbentity_surat_ayat_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_surat_ayat_proto_goTypes = []any{
	(*SuratAyat)(nil),             // 0: pbentity.SuratAyat
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_surat_ayat_proto_depIdxs = []int32{
	1, // 0: pbentity.SuratAyat.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // 1: pbentity.SuratAyat.UpdatedAt:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbentity_surat_ayat_proto_init() }
func file_pbentity_surat_ayat_proto_init() {
	if File_pbentity_surat_ayat_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_surat_ayat_proto_rawDesc), len(file_pbentity_surat_ayat_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_surat_ayat_proto_goTypes,
		DependencyIndexes: file_pbentity_surat_ayat_proto_depIdxs,
		MessageInfos:      file_pbentity_surat_ayat_proto_msgTypes,
	}.Build()
	File_pbentity_surat_ayat_proto = out.File
	file_pbentity_surat_ayat_proto_goTypes = nil
	file_pbentity_surat_ayat_proto_depIdxs = nil
}
