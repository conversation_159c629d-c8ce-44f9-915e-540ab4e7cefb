// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/news_article.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NewsArticle struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                      //
	LanguageZh    uint32                 `protobuf:"varint,2,opt,name=LanguageZh,proto3" json:"LanguageZh,omitempty" dc:"语言_zh，1启用，0关闭"`   // 语言_zh，1启用，0关闭
	LanguageEn    uint32                 `protobuf:"varint,3,opt,name=LanguageEn,proto3" json:"LanguageEn,omitempty" dc:"语言_en，1启用，0关闭"`   // 语言_en，1启用，0关闭
	LanguageId    uint32                 `protobuf:"varint,4,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言id，1启用，0关闭"`    // 语言id，1启用，0关闭
	CategoryId    uint32                 `protobuf:"varint,5,opt,name=CategoryId,proto3" json:"CategoryId,omitempty" dc:"分类id"`            // 分类id
	AdminId       uint32                 `protobuf:"varint,6,opt,name=AdminId,proto3" json:"AdminId,omitempty" dc:"分类负责人id"`               // 分类负责人id
	CoverImgs     string                 `protobuf:"bytes,7,opt,name=CoverImgs,proto3" json:"CoverImgs,omitempty" dc:"专题图片"`               // 专题图片
	Creater       uint32                 `protobuf:"varint,8,opt,name=Creater,proto3" json:"Creater,omitempty" dc:"创建者id"`                 // 创建者id
	CreateName    string                 `protobuf:"bytes,9,opt,name=CreateName,proto3" json:"CreateName,omitempty" dc:"后台创建者"`            // 后台创建者
	Author        string                 `protobuf:"bytes,10,opt,name=Author,proto3" json:"Author,omitempty" dc:"创建人"`                     // 创建人
	IsTop         uint32                 `protobuf:"varint,11,opt,name=IsTop,proto3" json:"IsTop,omitempty" dc:"是否加入头条，1启用，0关闭"`           // 是否加入头条，1启用，0关闭
	IsRecommend   uint32                 `protobuf:"varint,12,opt,name=IsRecommend,proto3" json:"IsRecommend,omitempty" dc:"是否推荐，1启用，0关闭"` // 是否推荐，1启用，0关闭
	IsPublish     uint32                 `protobuf:"varint,13,opt,name=IsPublish,proto3" json:"IsPublish,omitempty" dc:"是否发布，1启用，0关闭"`     // 是否发布，1启用，0关闭
	IsDraft       uint32                 `protobuf:"varint,14,opt,name=IsDraft,proto3" json:"IsDraft,omitempty" dc:"是否草稿状态，1是，0否"`         // 是否草稿状态，1是，0否
	CreateTime    int64                  `protobuf:"varint,15,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间"`           // 创建时间
	PublishTime   int64                  `protobuf:"varint,16,opt,name=PublishTime,proto3" json:"PublishTime,omitempty" dc:"发布时间"`         // 发布时间
	UpdateTime    int64                  `protobuf:"varint,17,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"修改时间"`           // 修改时间
	DeleteTime    int64                  `protobuf:"varint,18,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty" dc:"删除时间"`           // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsArticle) Reset() {
	*x = NewsArticle{}
	mi := &file_pbentity_news_article_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsArticle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsArticle) ProtoMessage() {}

func (x *NewsArticle) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_news_article_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsArticle.ProtoReflect.Descriptor instead.
func (*NewsArticle) Descriptor() ([]byte, []int) {
	return file_pbentity_news_article_proto_rawDescGZIP(), []int{0}
}

func (x *NewsArticle) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsArticle) GetLanguageZh() uint32 {
	if x != nil {
		return x.LanguageZh
	}
	return 0
}

func (x *NewsArticle) GetLanguageEn() uint32 {
	if x != nil {
		return x.LanguageEn
	}
	return 0
}

func (x *NewsArticle) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *NewsArticle) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *NewsArticle) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *NewsArticle) GetCoverImgs() string {
	if x != nil {
		return x.CoverImgs
	}
	return ""
}

func (x *NewsArticle) GetCreater() uint32 {
	if x != nil {
		return x.Creater
	}
	return 0
}

func (x *NewsArticle) GetCreateName() string {
	if x != nil {
		return x.CreateName
	}
	return ""
}

func (x *NewsArticle) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *NewsArticle) GetIsTop() uint32 {
	if x != nil {
		return x.IsTop
	}
	return 0
}

func (x *NewsArticle) GetIsRecommend() uint32 {
	if x != nil {
		return x.IsRecommend
	}
	return 0
}

func (x *NewsArticle) GetIsPublish() uint32 {
	if x != nil {
		return x.IsPublish
	}
	return 0
}

func (x *NewsArticle) GetIsDraft() uint32 {
	if x != nil {
		return x.IsDraft
	}
	return 0
}

func (x *NewsArticle) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *NewsArticle) GetPublishTime() int64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *NewsArticle) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *NewsArticle) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_news_article_proto protoreflect.FileDescriptor

const file_pbentity_news_article_proto_rawDesc = "" +
	"\n" +
	"\x1bpbentity/news_article.proto\x12\bpbentity\"\x99\x04\n" +
	"\vNewsArticle\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x1e\n" +
	"\n" +
	"LanguageZh\x18\x02 \x01(\rR\n" +
	"LanguageZh\x12\x1e\n" +
	"\n" +
	"LanguageEn\x18\x03 \x01(\rR\n" +
	"LanguageEn\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x04 \x01(\rR\n" +
	"LanguageId\x12\x1e\n" +
	"\n" +
	"CategoryId\x18\x05 \x01(\rR\n" +
	"CategoryId\x12\x18\n" +
	"\aAdminId\x18\x06 \x01(\rR\aAdminId\x12\x1c\n" +
	"\tCoverImgs\x18\a \x01(\tR\tCoverImgs\x12\x18\n" +
	"\aCreater\x18\b \x01(\rR\aCreater\x12\x1e\n" +
	"\n" +
	"CreateName\x18\t \x01(\tR\n" +
	"CreateName\x12\x16\n" +
	"\x06Author\x18\n" +
	" \x01(\tR\x06Author\x12\x14\n" +
	"\x05IsTop\x18\v \x01(\rR\x05IsTop\x12 \n" +
	"\vIsRecommend\x18\f \x01(\rR\vIsRecommend\x12\x1c\n" +
	"\tIsPublish\x18\r \x01(\rR\tIsPublish\x12\x18\n" +
	"\aIsDraft\x18\x0e \x01(\rR\aIsDraft\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\x0f \x01(\x03R\n" +
	"CreateTime\x12 \n" +
	"\vPublishTime\x18\x10 \x01(\x03R\vPublishTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\x11 \x01(\x03R\n" +
	"UpdateTime\x12\x1e\n" +
	"\n" +
	"DeleteTime\x18\x12 \x01(\x03R\n" +
	"DeleteTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_news_article_proto_rawDescOnce sync.Once
	file_pbentity_news_article_proto_rawDescData []byte
)

func file_pbentity_news_article_proto_rawDescGZIP() []byte {
	file_pbentity_news_article_proto_rawDescOnce.Do(func() {
		file_pbentity_news_article_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_news_article_proto_rawDesc), len(file_pbentity_news_article_proto_rawDesc)))
	})
	return file_pbentity_news_article_proto_rawDescData
}

var file_pbentity_news_article_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_news_article_proto_goTypes = []any{
	(*NewsArticle)(nil), // 0: pbentity.NewsArticle
}
var file_pbentity_news_article_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_news_article_proto_init() }
func file_pbentity_news_article_proto_init() {
	if File_pbentity_news_article_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_news_article_proto_rawDesc), len(file_pbentity_news_article_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_news_article_proto_goTypes,
		DependencyIndexes: file_pbentity_news_article_proto_depIdxs,
		MessageInfos:      file_pbentity_news_article_proto_msgTypes,
	}.Build()
	File_pbentity_news_article_proto = out.File
	file_pbentity_news_article_proto_goTypes = nil
	file_pbentity_news_article_proto_depIdxs = nil
}
