// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: islamic/v1/banner.proto

package islamicv1

import (
	common "halalplus/api/common"
	_ "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Banner列表请求
type BannerListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanguageId    uint32                 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"` // 语言ID: 0-中文, 1-英文, 2-印尼语
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BannerListReq) Reset() {
	*x = BannerListReq{}
	mi := &file_islamic_v1_banner_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BannerListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerListReq) ProtoMessage() {}

func (x *BannerListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerListReq.ProtoReflect.Descriptor instead.
func (*BannerListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{0}
}

func (x *BannerListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

// Banner信息
type BannerInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"Banner ID"`                              // Banner ID
	LanguageId    uint32                 `protobuf:"varint,2,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言ID"` // 语言ID
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"广告标题"`                              // 广告标题
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"广告描述"`                  // 广告描述
	ImageUrl      string                 `protobuf:"bytes,5,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty" dc:"广告图片URL"`     // 广告图片URL
	LinkUrl       string                 `protobuf:"bytes,6,opt,name=link_url,json=linkUrl,proto3" json:"link_url,omitempty" dc:"跳转链接URL"`        // 跳转链接URL
	SortOrder     uint32                 `protobuf:"varint,7,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty" dc:"排序权重"`    // 排序权重
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BannerInfo) Reset() {
	*x = BannerInfo{}
	mi := &file_islamic_v1_banner_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BannerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerInfo) ProtoMessage() {}

func (x *BannerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerInfo.ProtoReflect.Descriptor instead.
func (*BannerInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{1}
}

func (x *BannerInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BannerInfo) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *BannerInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BannerInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BannerInfo) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *BannerInfo) GetLinkUrl() string {
	if x != nil {
		return x.LinkUrl
	}
	return ""
}

func (x *BannerInfo) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

// Banner列表响应
type BannerListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          []*BannerInfo          `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BannerListRes) Reset() {
	*x = BannerListRes{}
	mi := &file_islamic_v1_banner_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BannerListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerListRes) ProtoMessage() {}

func (x *BannerListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerListRes.ProtoReflect.Descriptor instead.
func (*BannerListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{2}
}

func (x *BannerListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BannerListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BannerListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BannerListRes) GetData() []*BannerInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// Banner点击统计请求
type BannerClickReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BannerId      uint32                 `protobuf:"varint,1,opt,name=banner_id,json=bannerId,proto3" json:"banner_id,omitempty" dc:"Banner ID"` // Banner ID
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty" dc:"设备唯一标识"`     // 设备唯一标识
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BannerClickReq) Reset() {
	*x = BannerClickReq{}
	mi := &file_islamic_v1_banner_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BannerClickReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerClickReq) ProtoMessage() {}

func (x *BannerClickReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerClickReq.ProtoReflect.Descriptor instead.
func (*BannerClickReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{3}
}

func (x *BannerClickReq) GetBannerId() uint32 {
	if x != nil {
		return x.BannerId
	}
	return 0
}

func (x *BannerClickReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// Banner点击统计响应
type BannerClickRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty" dc:"操作结果消息"` // 操作结果消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BannerClickRes) Reset() {
	*x = BannerClickRes{}
	mi := &file_islamic_v1_banner_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BannerClickRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerClickRes) ProtoMessage() {}

func (x *BannerClickRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_banner_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerClickRes.ProtoReflect.Descriptor instead.
func (*BannerClickRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_banner_proto_rawDescGZIP(), []int{4}
}

func (x *BannerClickRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BannerClickRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BannerClickRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BannerClickRes) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_islamic_v1_banner_proto protoreflect.FileDescriptor

const file_islamic_v1_banner_proto_rawDesc = "" +
	"\n" +
	"\x17islamic/v1/banner.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\x1a\x15pbentity/banner.proto\"0\n" +
	"\rBannerListReq\x12\x1f\n" +
	"\vlanguage_id\x18\x01 \x01(\rR\n" +
	"languageId\"\xcc\x01\n" +
	"\n" +
	"BannerInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1f\n" +
	"\vlanguage_id\x18\x02 \x01(\rR\n" +
	"languageId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x1b\n" +
	"\timage_url\x18\x05 \x01(\tR\bimageUrl\x12\x19\n" +
	"\blink_url\x18\x06 \x01(\tR\alinkUrl\x12\x1d\n" +
	"\n" +
	"sort_order\x18\a \x01(\rR\tsortOrder\"\x86\x01\n" +
	"\rBannerListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12*\n" +
	"\x04data\x18\x04 \x03(\v2\x16.islamic.v1.BannerInfoR\x04data\"J\n" +
	"\x0eBannerClickReq\x12\x1b\n" +
	"\tbanner_id\x18\x01 \x01(\rR\bbannerId\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\"u\n" +
	"\x0eBannerClickRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage2\x9a\x01\n" +
	"\rBannerService\x12B\n" +
	"\n" +
	"BannerList\x12\x19.islamic.v1.BannerListReq\x1a\x19.islamic.v1.BannerListRes\x12E\n" +
	"\vBannerClick\x12\x1a.islamic.v1.BannerClickReq\x1a\x1a.islamic.v1.BannerClickResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_banner_proto_rawDescOnce sync.Once
	file_islamic_v1_banner_proto_rawDescData []byte
)

func file_islamic_v1_banner_proto_rawDescGZIP() []byte {
	file_islamic_v1_banner_proto_rawDescOnce.Do(func() {
		file_islamic_v1_banner_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_banner_proto_rawDesc), len(file_islamic_v1_banner_proto_rawDesc)))
	})
	return file_islamic_v1_banner_proto_rawDescData
}

var file_islamic_v1_banner_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_islamic_v1_banner_proto_goTypes = []any{
	(*BannerListReq)(nil),  // 0: islamic.v1.BannerListReq
	(*BannerInfo)(nil),     // 1: islamic.v1.BannerInfo
	(*BannerListRes)(nil),  // 2: islamic.v1.BannerListRes
	(*BannerClickReq)(nil), // 3: islamic.v1.BannerClickReq
	(*BannerClickRes)(nil), // 4: islamic.v1.BannerClickRes
	(*common.Error)(nil),   // 5: common.Error
}
var file_islamic_v1_banner_proto_depIdxs = []int32{
	5, // 0: islamic.v1.BannerListRes.error:type_name -> common.Error
	1, // 1: islamic.v1.BannerListRes.data:type_name -> islamic.v1.BannerInfo
	5, // 2: islamic.v1.BannerClickRes.error:type_name -> common.Error
	0, // 3: islamic.v1.BannerService.BannerList:input_type -> islamic.v1.BannerListReq
	3, // 4: islamic.v1.BannerService.BannerClick:input_type -> islamic.v1.BannerClickReq
	2, // 5: islamic.v1.BannerService.BannerList:output_type -> islamic.v1.BannerListRes
	4, // 6: islamic.v1.BannerService.BannerClick:output_type -> islamic.v1.BannerClickRes
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_islamic_v1_banner_proto_init() }
func file_islamic_v1_banner_proto_init() {
	if File_islamic_v1_banner_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_banner_proto_rawDesc), len(file_islamic_v1_banner_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_banner_proto_goTypes,
		DependencyIndexes: file_islamic_v1_banner_proto_depIdxs,
		MessageInfos:      file_islamic_v1_banner_proto_msgTypes,
	}.Build()
	File_islamic_v1_banner_proto = out.File
	file_islamic_v1_banner_proto_goTypes = nil
	file_islamic_v1_banner_proto_depIdxs = nil
}
