syntax = "proto3";

package islamic.v1;

import "google/api/annotations.proto";
import "common/base.proto";

option go_package = "halalplus/api/islamic/v1;v1";

// =================================================================================================
// Calendar Service
// Provides functionalities for fetching calendar data, prayer times, and special events.
// =================================================================================================
service Calendar {
    // Get calendar data for a specific month.
    // It returns a list of days, each containing Gregorian, Hijri, prayer times, and event info.
    rpc GetCalendarMonth(GetCalendarMonthReq) returns (GetCalendarMonthResp) {
        option (google.api.http) = {
            get: "/v1/calendar/{year}/{month}"
        };
    }

    // ----------------- Admin APIs for Events -----------------

    // Create a new calendar event (e.g., holiday, fasting day).
    rpc CreateCalendarEvent(CreateCalendarEventReq) returns (CreateCalendarEventResp) {
        option (google.api.http) = {
            post: "/v1/calendar/events"
            body: "*"
        };
    }

    // Get details of a specific calendar event.
    rpc GetCalendarEvent(GetCalendarEventReq) returns (CalendarEvent) {
        option (google.api.http) = {
            get: "/v1/calendar/events/{id}"
        };
    }

    // Update an existing calendar event.
    rpc UpdateCalendarEvent(UpdateCalendarEventReq) returns (common.BaseResp) {
        option (google.api.http) = {
            put: "/v1/calendar/events/{id}"
            body: "*"
        };
    }

    // Delete a calendar event.
    rpc DeleteCalendarEvent(DeleteCalendarEventReq) returns (common.BaseResp) {
         option (google.api.http) = {
            delete: "/v1/calendar/events/{id}"
        };
    }

    // List all calendar events with optional filters.
    rpc ListCalendarEvents(ListCalendarEventsReq) returns (ListCalendarEventsResp) {
        option (google.api.http) = {
            get: "/v1/calendar/events"
        };
    }
}

// ========== Enums ==========

// Type of calendar event for categorization and client-side rendering.
enum CalendarEventType {
    CALENDAR_EVENT_TYPE_UNSPECIFIED = 0;
    PUBLIC_HOLIDAY = 1; // Represents "Hari Besar & Libur Nasional".
    FASTING = 2;        // Represents "Puasa" days.
    IMPORTANT_DAY = 3;  // Represents other significant Islamic days.
}

// Source of the event data, for tracking and management.
enum CalendarEventSource {
    CALENDAR_EVENT_SOURCE_UNSPECIFIED = 0;
    MANUAL = 1;  // Manually entered by an admin.
    SCRAPER = 2; // Imported via a web scraper.
    SYSTEM = 3;  // Pre-populated system data.
}


// ========== Core Data Structures ==========

// Prayer times for a single day.
message PrayerTimes {
    string fajr = 1;    // Fajr prayer time (HH:mm)
    string sunrise = 2; // Sunrise time (HH:mm)
    string dhuhr = 3;   // Dhuhr prayer time (HH:mm)
    string asr = 4;     // Asr prayer time (HH:mm)
    string maghrib = 5; // Maghrib prayer time (HH:mm)
    string isha = 6;    // Isha prayer time (HH:mm)
    string imsak = 7;   // Imsak time (HH:mm)
}

// Hijri (Islamic) calendar date details.
message HijriDate {
    string date_str = 1;        // Hijri date in string format (e.g., "1447-01-01").
    int32 year = 2;             // Hijri year.
    int32 month = 3;            // Hijri month.
    int32 day = 4;              // Hijri day.
    string month_name_en = 5;   // Hijri month name in English.
    string month_name_ar = 6;   // Hijri month name in Arabic.
    string day_of_week_en = 7;  // Day of the week in English.
    string day_of_week_ar = 8;  // Day of the week in Arabic.
}

// Represents a special event on the calendar.
message CalendarEvent {
    int64 id = 1;
    string title = 2;
    string description = 3;
    CalendarEventType event_type = 4;
    string start_date = 5; // Start date in YYYY-MM-DD format.
    string end_date = 6;   // End date in YYYY-MM-DD format.
    string jump_url = 7;
    CalendarEventSource source = 8;
    bool is_active = 9;
    string created_at = 10;
    string updated_at = 11;
}

// Represents a single day in the calendar view.
message CalendarDay {
    int32 gregorian_year = 1;
    int32 gregorian_month = 2;
    int32 gregorian_day = 3;
    HijriDate hijri = 4;
    PrayerTimes prayer_times = 5;
    repeated CalendarEvent events = 6; // List of events occurring on this day.
}

// ========== RPC Request & Response Messages ==========

// Request for GetCalendarMonth RPC.
message GetCalendarMonthReq {
    int32 year = 1;  // Gregorian year.
    int32 month = 2; // Gregorian month.
}

// Response for GetCalendarMonth RPC.
message GetCalendarMonthResp {
    repeated CalendarDay days = 1;
}

// Request for CreateCalendarEvent RPC.
message CreateCalendarEventReq {
    string title = 1;
    string description = 2;
    CalendarEventType event_type = 3;
    string start_date = 4; // YYYY-MM-DD
    string end_date = 5;   // YYYY-MM-DD
    string jump_url = 6;
    CalendarEventSource source = 7;
}

// Response for CreateCalendarEvent RPC.
message CreateCalendarEventResp {
    int64 id = 1;
}

// Request for GetCalendarEvent RPC.
message GetCalendarEventReq {
    int64 id = 1;
}

// Request for UpdateCalendarEvent RPC.
message UpdateCalendarEventReq {
    int64 id = 1;
    string title = 2;
    string description = 3;
    CalendarEventType event_type = 4;
    string start_date = 5; // YYYY-MM-DD
    string end_date = 6;   // YYYY-MM-DD
    string jump_url = 7;
    bool is_active = 8;
}

// Request for DeleteCalendarEvent RPC.
message DeleteCalendarEventReq {
    int64 id = 1;
}

// Request for ListCalendarEvents RPC.
message ListCalendarEventsReq {
    common.Paging page_info = 1;
    int32 year = 2;  // Optional: filter by year.
    int32 month = 3; // Optional: filter by month.
    CalendarEventType event_type = 4; // Optional: filter by event type.
}

// Response for ListCalendarEvents RPC.
message ListCalendarEventsResp {
    repeated CalendarEvent events = 1;
    int32 total = 2;
}
