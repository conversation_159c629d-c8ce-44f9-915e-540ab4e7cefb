// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: islamic/v1/surah.proto

package islamicv1

import (
	common "halalplus/api/common"
	pbentity "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 古兰经-章-列表
type SurahListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" dc:"章节id"`   // 章节id
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"名称"` //名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahListReq) Reset() {
	*x = SurahListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListReq) ProtoMessage() {}

func (x *SurahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListReq.ProtoReflect.Descriptor instead.
func (*SurahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{0}
}

func (x *SurahListReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SurahListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type SurahListRes struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error           `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          []*pbentity.SuratDaftar `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahListRes) Reset() {
	*x = SurahListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListRes) ProtoMessage() {}

func (x *SurahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListRes.ProtoReflect.Descriptor instead.
func (*SurahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{1}
}

func (x *SurahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SurahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SurahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SurahListRes) GetData() []*pbentity.SuratDaftar {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type JuzListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" dc:"节id"`        // 节id
	Nomor         string                 `protobuf:"bytes,2,opt,name=nomor,proto3" json:"nomor,omitempty" dc:"章节id"` //章节id
	Idn           string                 `protobuf:"bytes,3,opt,name=idn,proto3" json:"idn,omitempty" dc:"印尼语翻译"`    //印尼语翻译
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JuzListReq) Reset() {
	*x = JuzListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JuzListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListReq) ProtoMessage() {}

func (x *JuzListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListReq.ProtoReflect.Descriptor instead.
func (*JuzListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{2}
}

func (x *JuzListReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *JuzListReq) GetNomor() string {
	if x != nil {
		return x.Nomor
	}
	return ""
}

func (x *JuzListReq) GetIdn() string {
	if x != nil {
		return x.Idn
	}
	return ""
}

type JuzListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          []*pbentity.SuratAyat  `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JuzListRes) Reset() {
	*x = JuzListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JuzListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListRes) ProtoMessage() {}

func (x *JuzListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListRes.ProtoReflect.Descriptor instead.
func (*JuzListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{3}
}

func (x *JuzListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *JuzListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *JuzListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *JuzListRes) GetData() []*pbentity.SuratAyat {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-章-信息
type SurahInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" dc:"节id"`        // 节id
	Nomor         string                 `protobuf:"bytes,2,opt,name=nomor,proto3" json:"nomor,omitempty" dc:"章节id"` //章节id
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`     //名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahInfoReq) Reset() {
	*x = SurahInfoReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahInfoReq) ProtoMessage() {}

func (x *SurahInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahInfoReq.ProtoReflect.Descriptor instead.
func (*SurahInfoReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{4}
}

func (x *SurahInfoReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SurahInfoReq) GetNomor() string {
	if x != nil {
		return x.Nomor
	}
	return ""
}

func (x *SurahInfoReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type SurahInfoRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *pbentity.SuratDaftar  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahInfoRes) Reset() {
	*x = SurahInfoRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahInfoRes) ProtoMessage() {}

func (x *SurahInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahInfoRes.ProtoReflect.Descriptor instead.
func (*SurahInfoRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{5}
}

func (x *SurahInfoRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SurahInfoRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SurahInfoRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SurahInfoRes) GetData() *pbentity.SuratDaftar {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-章-解释-信息
type SurahDescReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" dc:"节id"`                   // 节id
	Nomor         string                 `protobuf:"bytes,2,opt,name=nomor,proto3" json:"nomor,omitempty" dc:"章节id"`            //章节id
	JuzId         string                 `protobuf:"bytes,3,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"名称"` //名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahDescReq) Reset() {
	*x = SurahDescReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahDescReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahDescReq) ProtoMessage() {}

func (x *SurahDescReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahDescReq.ProtoReflect.Descriptor instead.
func (*SurahDescReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{6}
}

func (x *SurahDescReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SurahDescReq) GetNomor() string {
	if x != nil {
		return x.Nomor
	}
	return ""
}

func (x *SurahDescReq) GetJuzId() string {
	if x != nil {
		return x.JuzId
	}
	return ""
}

type SurahDescRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *pbentity.SuratTafsir  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahDescRes) Reset() {
	*x = SurahDescRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahDescRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahDescRes) ProtoMessage() {}

func (x *SurahDescRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahDescRes.ProtoReflect.Descriptor instead.
func (*SurahDescRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{7}
}

func (x *SurahDescRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SurahDescRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SurahDescRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SurahDescRes) GetData() *pbentity.SuratTafsir {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_surah_proto protoreflect.FileDescriptor

const file_islamic_v1_surah_proto_rawDesc = "" +
	"\n" +
	"\x16islamic/v1/surah.proto\x12\n" +
	"islamic.v1\x1a\x19pbentity/surat_ayat.proto\x1a\x1bpbentity/surat_daftar.proto\x1a\x1bpbentity/surat_tafsir.proto\x1a\x17common/front_info.proto\x1a\x11common/base.proto\"2\n" +
	"\fSurahListReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\x84\x01\n" +
	"\fSurahListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12)\n" +
	"\x04data\x18\x04 \x03(\v2\x15.pbentity.SuratDaftarR\x04data\"D\n" +
	"\n" +
	"JuzListReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05nomor\x18\x02 \x01(\tR\x05nomor\x12\x10\n" +
	"\x03idn\x18\x03 \x01(\tR\x03idn\"\x80\x01\n" +
	"\n" +
	"JuzListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12'\n" +
	"\x04data\x18\x04 \x03(\v2\x13.pbentity.SuratAyatR\x04data\"H\n" +
	"\fSurahInfoReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05nomor\x18\x02 \x01(\tR\x05nomor\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"\x84\x01\n" +
	"\fSurahInfoRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12)\n" +
	"\x04data\x18\x04 \x01(\v2\x15.pbentity.SuratDaftarR\x04data\"K\n" +
	"\fSurahDescReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05nomor\x18\x02 \x01(\tR\x05nomor\x12\x15\n" +
	"\x06juz_id\x18\x03 \x01(\tR\x05juzId\"\x84\x01\n" +
	"\fSurahDescRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12)\n" +
	"\x04data\x18\x04 \x01(\v2\x15.pbentity.SuratTafsirR\x04data2\x8c\x02\n" +
	"\fSurahService\x12?\n" +
	"\tSurahList\x12\x18.islamic.v1.SurahListReq\x1a\x18.islamic.v1.SurahListRes\x129\n" +
	"\aJuzList\x12\x16.islamic.v1.JuzListReq\x1a\x16.islamic.v1.JuzListRes\x12?\n" +
	"\tSurahInfo\x12\x18.islamic.v1.SurahInfoReq\x1a\x18.islamic.v1.SurahInfoRes\x12?\n" +
	"\tSurahDesc\x12\x18.islamic.v1.SurahDescReq\x1a\x18.islamic.v1.SurahDescResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_surah_proto_rawDescOnce sync.Once
	file_islamic_v1_surah_proto_rawDescData []byte
)

func file_islamic_v1_surah_proto_rawDescGZIP() []byte {
	file_islamic_v1_surah_proto_rawDescOnce.Do(func() {
		file_islamic_v1_surah_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_surah_proto_rawDesc), len(file_islamic_v1_surah_proto_rawDesc)))
	})
	return file_islamic_v1_surah_proto_rawDescData
}

var file_islamic_v1_surah_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_islamic_v1_surah_proto_goTypes = []any{
	(*SurahListReq)(nil),         // 0: islamic.v1.SurahListReq
	(*SurahListRes)(nil),         // 1: islamic.v1.SurahListRes
	(*JuzListReq)(nil),           // 2: islamic.v1.JuzListReq
	(*JuzListRes)(nil),           // 3: islamic.v1.JuzListRes
	(*SurahInfoReq)(nil),         // 4: islamic.v1.SurahInfoReq
	(*SurahInfoRes)(nil),         // 5: islamic.v1.SurahInfoRes
	(*SurahDescReq)(nil),         // 6: islamic.v1.SurahDescReq
	(*SurahDescRes)(nil),         // 7: islamic.v1.SurahDescRes
	(*common.Error)(nil),         // 8: common.Error
	(*pbentity.SuratDaftar)(nil), // 9: pbentity.SuratDaftar
	(*pbentity.SuratAyat)(nil),   // 10: pbentity.SuratAyat
	(*pbentity.SuratTafsir)(nil), // 11: pbentity.SuratTafsir
}
var file_islamic_v1_surah_proto_depIdxs = []int32{
	8,  // 0: islamic.v1.SurahListRes.error:type_name -> common.Error
	9,  // 1: islamic.v1.SurahListRes.data:type_name -> pbentity.SuratDaftar
	8,  // 2: islamic.v1.JuzListRes.error:type_name -> common.Error
	10, // 3: islamic.v1.JuzListRes.data:type_name -> pbentity.SuratAyat
	8,  // 4: islamic.v1.SurahInfoRes.error:type_name -> common.Error
	9,  // 5: islamic.v1.SurahInfoRes.data:type_name -> pbentity.SuratDaftar
	8,  // 6: islamic.v1.SurahDescRes.error:type_name -> common.Error
	11, // 7: islamic.v1.SurahDescRes.data:type_name -> pbentity.SuratTafsir
	0,  // 8: islamic.v1.SurahService.SurahList:input_type -> islamic.v1.SurahListReq
	2,  // 9: islamic.v1.SurahService.JuzList:input_type -> islamic.v1.JuzListReq
	4,  // 10: islamic.v1.SurahService.SurahInfo:input_type -> islamic.v1.SurahInfoReq
	6,  // 11: islamic.v1.SurahService.SurahDesc:input_type -> islamic.v1.SurahDescReq
	1,  // 12: islamic.v1.SurahService.SurahList:output_type -> islamic.v1.SurahListRes
	3,  // 13: islamic.v1.SurahService.JuzList:output_type -> islamic.v1.JuzListRes
	5,  // 14: islamic.v1.SurahService.SurahInfo:output_type -> islamic.v1.SurahInfoRes
	7,  // 15: islamic.v1.SurahService.SurahDesc:output_type -> islamic.v1.SurahDescRes
	12, // [12:16] is the sub-list for method output_type
	8,  // [8:12] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_islamic_v1_surah_proto_init() }
func file_islamic_v1_surah_proto_init() {
	if File_islamic_v1_surah_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_surah_proto_rawDesc), len(file_islamic_v1_surah_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_surah_proto_goTypes,
		DependencyIndexes: file_islamic_v1_surah_proto_depIdxs,
		MessageInfos:      file_islamic_v1_surah_proto_msgTypes,
	}.Build()
	File_islamic_v1_surah_proto = out.File
	file_islamic_v1_surah_proto_goTypes = nil
	file_islamic_v1_surah_proto_depIdxs = nil
}
