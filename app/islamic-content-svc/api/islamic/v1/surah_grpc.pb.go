// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: islamic/v1/surah.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SurahService_SurahList_FullMethodName = "/islamic.v1.SurahService/SurahList"
	SurahService_JuzList_FullMethodName   = "/islamic.v1.SurahService/JuzList"
	SurahService_SurahInfo_FullMethodName = "/islamic.v1.SurahService/SurahInfo"
	SurahService_SurahDesc_FullMethodName = "/islamic.v1.SurahService/SurahDesc"
)

// SurahServiceClient is the client API for SurahService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SurahServiceClient interface {
	SurahList(ctx context.Context, in *SurahListReq, opts ...grpc.CallOption) (*SurahListRes, error)
	JuzList(ctx context.Context, in *JuzListReq, opts ...grpc.CallOption) (*JuzListRes, error)
	SurahInfo(ctx context.Context, in *SurahInfoReq, opts ...grpc.CallOption) (*SurahInfoRes, error)
	SurahDesc(ctx context.Context, in *SurahDescReq, opts ...grpc.CallOption) (*SurahDescRes, error)
}

type surahServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSurahServiceClient(cc grpc.ClientConnInterface) SurahServiceClient {
	return &surahServiceClient{cc}
}

func (c *surahServiceClient) SurahList(ctx context.Context, in *SurahListReq, opts ...grpc.CallOption) (*SurahListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurahListRes)
	err := c.cc.Invoke(ctx, SurahService_SurahList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) JuzList(ctx context.Context, in *JuzListReq, opts ...grpc.CallOption) (*JuzListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JuzListRes)
	err := c.cc.Invoke(ctx, SurahService_JuzList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) SurahInfo(ctx context.Context, in *SurahInfoReq, opts ...grpc.CallOption) (*SurahInfoRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurahInfoRes)
	err := c.cc.Invoke(ctx, SurahService_SurahInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) SurahDesc(ctx context.Context, in *SurahDescReq, opts ...grpc.CallOption) (*SurahDescRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurahDescRes)
	err := c.cc.Invoke(ctx, SurahService_SurahDesc_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SurahServiceServer is the server API for SurahService service.
// All implementations must embed UnimplementedSurahServiceServer
// for forward compatibility.
type SurahServiceServer interface {
	SurahList(context.Context, *SurahListReq) (*SurahListRes, error)
	JuzList(context.Context, *JuzListReq) (*JuzListRes, error)
	SurahInfo(context.Context, *SurahInfoReq) (*SurahInfoRes, error)
	SurahDesc(context.Context, *SurahDescReq) (*SurahDescRes, error)
	mustEmbedUnimplementedSurahServiceServer()
}

// UnimplementedSurahServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSurahServiceServer struct{}

func (UnimplementedSurahServiceServer) SurahList(context.Context, *SurahListReq) (*SurahListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurahList not implemented")
}
func (UnimplementedSurahServiceServer) JuzList(context.Context, *JuzListReq) (*JuzListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JuzList not implemented")
}
func (UnimplementedSurahServiceServer) SurahInfo(context.Context, *SurahInfoReq) (*SurahInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurahInfo not implemented")
}
func (UnimplementedSurahServiceServer) SurahDesc(context.Context, *SurahDescReq) (*SurahDescRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurahDesc not implemented")
}
func (UnimplementedSurahServiceServer) mustEmbedUnimplementedSurahServiceServer() {}
func (UnimplementedSurahServiceServer) testEmbeddedByValue()                      {}

// UnsafeSurahServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SurahServiceServer will
// result in compilation errors.
type UnsafeSurahServiceServer interface {
	mustEmbedUnimplementedSurahServiceServer()
}

func RegisterSurahServiceServer(s grpc.ServiceRegistrar, srv SurahServiceServer) {
	// If the following call pancis, it indicates UnimplementedSurahServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SurahService_ServiceDesc, srv)
}

func _SurahService_SurahList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurahListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).SurahList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_SurahList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).SurahList(ctx, req.(*SurahListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_JuzList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JuzListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).JuzList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_JuzList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).JuzList(ctx, req.(*JuzListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_SurahInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurahInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).SurahInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_SurahInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).SurahInfo(ctx, req.(*SurahInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_SurahDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurahDescReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).SurahDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_SurahDesc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).SurahDesc(ctx, req.(*SurahDescReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SurahService_ServiceDesc is the grpc.ServiceDesc for SurahService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SurahService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.SurahService",
	HandlerType: (*SurahServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SurahList",
			Handler:    _SurahService_SurahList_Handler,
		},
		{
			MethodName: "JuzList",
			Handler:    _SurahService_JuzList_Handler,
		},
		{
			MethodName: "SurahInfo",
			Handler:    _SurahService_SurahInfo_Handler,
		},
		{
			MethodName: "SurahDesc",
			Handler:    _SurahService_SurahDesc_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/surah.proto",
}
