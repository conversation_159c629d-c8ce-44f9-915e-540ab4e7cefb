package main

import (
	"fmt"
	"time"

	"halalplus/app/islamic-content-svc/internal/logic/calendar"
)

func main() {
	// 测试辅助方法
	cal := calendar.TestHelper()

	// 测试星期名称转换
	fmt.Println("Testing weekday names:")
	for i := int32(0); i < 7; i++ {
		fmt.Printf("Weekday %d: %s\n", i, cal.getWeekdayName(i))
	}

	// 测试Pasaran名称转换
	fmt.Println("\nTesting pasaran names:")
	for i := int32(0); i < 5; i++ {
		fmt.Printf("Pasaran %d: %s\n", i, cal.getPasaranName(i))
	}

	// 测试事件类型名称转换
	fmt.Println("\nTesting event type names:")
	eventTypes := []string{"HARI_BESAR", "LIBUR_NASIONAL", "PUASA", "UNKNOWN"}
	for _, eventType := range eventTypes {
		fmt.Printf("Event type %s: %s\n", eventType, cal.getEventTypeName(eventType))
	}

	// 测试Pasaran计算
	fmt.Println("\nTesting pasaran calculation:")
	testDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	pasaran := cal.calculatePasaran(testDate)
	fmt.Printf("Date %s: Pasaran=%d (%s)\n", testDate.Format("2006-01-02"), pasaran, cal.getPasaranName(int32(pasaran)))

	// 测试Hijriah日期计算
	fmt.Println("\nTesting Hijriah date calculation:")
	hijriahInfo, err := cal.calculateUmmulQura(2024, 1, 1, 0)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Gregorian 2024-01-01 -> Hijriah %d-%02d-%02d\n",
			hijriahInfo.HijriahYear, hijriahInfo.HijriahMonth, hijriahInfo.HijriahDay)
		fmt.Printf("Weekday: %d (%s), Pasaran: %d (%s)\n",
			hijriahInfo.Weekday, cal.getWeekdayName(int32(hijriahInfo.Weekday)),
			hijriahInfo.Pasaran, cal.getPasaranName(int32(hijriahInfo.Pasaran)))
	}
}
