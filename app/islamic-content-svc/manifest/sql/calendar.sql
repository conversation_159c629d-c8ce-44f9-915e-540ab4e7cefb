-- 伊斯兰日历系统相关表结构

-- Hijriah日历数据表
-- 公历对应的伊斯兰历
-- method_code: LFNU, UMMUL_QURA。LFNU暂时没找到数据来源，UMMUL_QURA可以通过计算获取。所以很有可能初期阶段这个表是空的
CREATE TABLE hijriah_calendar (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    gregorian_date DATE NOT NULL COMMENT '公历日期',
    hijriah_year INT NOT NULL COMMENT 'Hijriah年',
    hijriah_month INT NOT NULL COMMENT 'Hijriah月',
    hijriah_day INT NOT NULL COMMENT 'Hijriah日',
    method_code VARCHAR(20) NOT NULL COMMENT '计算方法代码，如：LFNU, UMMUL_QURA',
    java_day VARCHAR(50) COMMENT 'Java日历（Pasaran），如：Senin <PERSON>',
    weekday_name VARCHAR(20) COMMENT '星期名称，如：<PERSON><PERSON>, <PERSON><PERSON><PERSON>',
    pasaran_name VARCHAR(20) COMMENT '<PERSON><PERSON><PERSON>名称，如：<PERSON><PERSON>, Legi',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_date_method (gregorian_date, method_code) COMMENT '公历日期+计算方法唯一索引',
    INDEX idx_hijriah_date (hijriah_year, hijriah_month, hijriah_day) COMMENT 'Hijriah日期索引',
    INDEX idx_gregorian_date (gregorian_date) COMMENT '公历日期索引',
    INDEX idx_gregorian_year_month (YEAR(gregorian_date), MONTH(gregorian_date)) COMMENT '公历年月索引',
    INDEX idx_method_code (method_code) COMMENT '计算方法代码索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Hijriah日历数据表';

-- 日历事件表
CREATE TABLE calendar_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    event_type ENUM('HARI_BESAR', 'LIBUR_NASIONAL', 'PUASA') NOT NULL COMMENT '事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒',
    title VARCHAR(200) NOT NULL COMMENT '事件标题',
    description TEXT COMMENT '事件描述',
    gregorian_date DATE NOT NULL COMMENT '公历日期',
    jump_url VARCHAR(500) COMMENT '点击跳转链接',
    data_source VARCHAR(20) DEFAULT 'MANUAL' COMMENT '数据来源：MANUAL-人工录入，CRAWLER-爬虫获取',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_event_type (event_type) COMMENT '事件类型索引',
    INDEX idx_gregorian_date (gregorian_date) COMMENT '公历日期索引',
    INDEX idx_gregorian_year_month (YEAR(gregorian_date), MONTH(gregorian_date)) COMMENT '公历年月索引',
    INDEX idx_data_source (data_source) COMMENT '数据来源索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日历事件表';

-- 用户日历设置表
CREATE TABLE user_calendar_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    calculation_method ENUM('AUTO', 'LFNU', 'UMMUL_QURA') DEFAULT 'AUTO' COMMENT '方法选择：AUTO-自动，LFNU-Lembaga Falakiyah NU，UMMUL_QURA-Ummul Qura',
    date_adjustment INT DEFAULT 0 COMMENT 'Hijriah日期校正：-3到+3天的偏移量，0为自动（默认）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_id (user_id) COMMENT '用户ID唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户日历设置表';

-- 初始化基础数据已通过ENUM定义在表结构中
