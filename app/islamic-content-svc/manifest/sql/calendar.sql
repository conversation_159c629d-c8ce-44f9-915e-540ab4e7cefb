-- 伊斯兰日历系统相关表结构

-- 1. Hijriah日历数据表
CREATE TABLE hijriah_calendar (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    gregorian_date DATE NOT NULL COMMENT '公历日期',
    hijriah_year INT NOT NULL COMMENT 'Hijriah年',
    hijriah_month INT NOT NULL COMMENT 'Hijriah月',
    hijriah_day INT NOT NULL COMMENT 'Hijriah日',
    method_code VARCHAR(20) NOT NULL COMMENT '计算方法代码，如：LFNU, UMMUL_QURA',
    java_day VARCHAR(50) COMMENT 'Java日历（Pasaran），如：Senin K<PERSON>',
    weekday_name VARCHAR(20) COMMENT '星期名称，如：Senin, Selasa',
    pasaran_name VARCHAR(20) COMMENT 'Pasaran名称，如：Kliwon, Legi',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_date_method (gregorian_date, method_code) COMMENT '公历日期+计算方法唯一索引',
    INDEX idx_hijriah_date (hijriah_year, hijriah_month, hijriah_day) COMMENT 'Hijriah日期索引',
    INDEX idx_gregorian_date (gregorian_date) COMMENT '公历日期索引',
    INDEX idx_method_code (method_code) COMMENT '计算方法代码索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Hijriah日历数据表';

-- 2. 事件类型表
CREATE TABLE calendar_event_types (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    type_code VARCHAR(50) NOT NULL UNIQUE COMMENT '事件类型代码，如：HARI_BESAR, LIBUR_NASIONAL, PUASA',
    type_name VARCHAR(100) NOT NULL COMMENT '事件类型名称',
    description TEXT COMMENT '类型描述',
    icon VARCHAR(100) COMMENT '图标路径或类名',
    color VARCHAR(20) COMMENT '显示颜色',
    is_clickable TINYINT(1) DEFAULT 0 COMMENT '是否支持点击跳转：0-不支持，1-支持',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日历事件类型表';

-- 3. 日历事件表
CREATE TABLE calendar_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    event_type_id BIGINT NOT NULL COMMENT '事件类型ID，关联calendar_event_types表',
    title VARCHAR(200) NOT NULL COMMENT '事件标题',
    description TEXT COMMENT '事件描述',
    gregorian_date DATE COMMENT '公历日期',
    hijriah_year INT COMMENT 'Hijriah年',
    hijriah_month INT COMMENT 'Hijriah月',
    hijriah_day INT COMMENT 'Hijriah日',
    is_recurring TINYINT(1) DEFAULT 0 COMMENT '是否重复事件：0-单次，1-重复',
    recurring_type VARCHAR(20) COMMENT '重复类型：YEARLY, MONTHLY, WEEKLY',
    start_date DATE COMMENT '开始日期（用于重复事件）',
    end_date DATE COMMENT '结束日期（用于重复事件）',
    jump_url VARCHAR(500) COMMENT '点击跳转链接',
    data_source VARCHAR(20) DEFAULT 'MANUAL' COMMENT '数据来源：MANUAL-人工录入，CRAWLER-爬虫获取',
    source_url VARCHAR(500) COMMENT '数据源URL（爬虫来源）',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_event_type (event_type_id) COMMENT '事件类型索引',
    INDEX idx_gregorian_date (gregorian_date) COMMENT '公历日期索引',
    INDEX idx_hijriah_date (hijriah_year, hijriah_month, hijriah_day) COMMENT 'Hijriah日期索引',
    INDEX idx_data_source (data_source) COMMENT '数据来源索引',
    FOREIGN KEY (event_type_id) REFERENCES calendar_event_types(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日历事件表';

-- 4. 用户日历设置表
CREATE TABLE user_calendar_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    preferred_method_code VARCHAR(20) DEFAULT 'UMMUL_QURA' COMMENT '首选Hijriah计算方法代码',
    show_hijriah TINYINT(1) DEFAULT 1 COMMENT '是否显示Hijriah日期：0-不显示，1-显示',
    show_java_day TINYINT(1) DEFAULT 1 COMMENT '是否显示Java日历：0-不显示，1-显示',
    default_view VARCHAR(20) DEFAULT 'MONTH' COMMENT '默认视图：MONTH-月视图，WEEK-周视图，DAY-日视图',
    timezone VARCHAR(50) DEFAULT 'Asia/Jakarta' COMMENT '时区设置',
    language VARCHAR(10) DEFAULT 'id' COMMENT '语言设置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_id (user_id) COMMENT '用户ID唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户日历设置表';

-- 初始化基础数据
INSERT INTO hijriah_calculation_methods (method_code, method_name, description, is_calculated) VALUES
('UMMUL_QURA', 'Ummul Qura', '沙特阿拉伯使用的Hijriah历法，可通过计算获取', 1),
('LFNU', 'Lembaga Falakiyah NU', 'Nahdlatul Ulama天文机构历法，需要通过数据源获取', 0);

INSERT INTO calendar_event_types (type_code, type_name, description, is_clickable, sort_order) VALUES
('HARI_BESAR', 'Hari Besar', '重大伊斯兰节日', 1, 1),
('LIBUR_NASIONAL', 'Libur Nasional', '国定假日', 1, 2),
('PUASA', 'Puasa', '斋戒相关事件', 1, 3);
