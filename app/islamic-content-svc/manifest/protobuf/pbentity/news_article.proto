// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsArticle {
    uint32 Id          = 1;  //
    uint32 LanguageZh  = 2;  // 语言_zh，1启用，0关闭
    uint32 LanguageEn  = 3;  // 语言_en，1启用，0关闭
    uint32 LanguageId  = 4;  // 语言id，1启用，0关闭
    uint32 CategoryId  = 5;  // 分类id
    uint32 AdminId     = 6;  // 分类负责人id
    string CoverImgs   = 7;  // 专题图片
    uint32 Creater     = 8;  // 创建者id
    string CreateName  = 9;  // 后台创建者
    string Author      = 10; // 创建人
    uint32 IsTop       = 11; // 是否加入头条，1启用，0关闭
    uint32 IsRecommend = 12; // 是否推荐，1启用，0关闭
    uint32 IsPublish   = 13; // 是否发布，1启用，0关闭
    uint32 IsDraft     = 14; // 是否草稿状态，1是，0否
    int64  CreateTime  = 15; // 创建时间
    int64  PublishTime = 16; // 发布时间
    int64  UpdateTime  = 17; // 修改时间
    int64  DeleteTime  = 18; // 删除时间
}