// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsTopic {
    uint32 Id         = 1;  //
    uint32 Counts     = 2;  // 文章数量
    uint32 Status     = 3;  // 是否显示，1启用，0关闭
    uint32 Sort       = 4;  // 排序，数字越小，排序越靠前
    uint32 AdminId    = 5;  // 分类负责人id
    string TopicImgs  = 6;  // 专题图片
    uint32 Creater    = 7;  // 创建者id
    string CreateName = 8;  // 创建者
    int64  CreateTime = 9;  // 创建时间
    int64  UpdateTime = 10; // 修改时间
    int64  DeleteTime = 11; // 删除时间
}