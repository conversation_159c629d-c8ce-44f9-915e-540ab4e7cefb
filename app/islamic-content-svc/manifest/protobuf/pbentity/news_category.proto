// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsCategory {
    uint32 Id         = 1;  //
    uint32 ParentId   = 2;  // 上级id，0表示顶级
    uint32 Status     = 3;  // 状态，1启用，0关闭
    uint32 Sort       = 4;  // 排序，数字越小，排序越靠前
    uint32 AdminId    = 5;  // 分类负责人id
    string CoverImgs  = 6;  // 封面图
    string Remark     = 7;  // 备注
    uint32 Creater    = 8;  // 创建者id
    string CreateName = 9;  // 创建者
    int64  CreateTime = 10; // 创建时间
    int64  UpdateTime = 11; // 修改时间
    int64  DeleteTime = 12; // 删除时间
}