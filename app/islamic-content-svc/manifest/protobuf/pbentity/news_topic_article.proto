// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsTopicArticle {
    uint32 Id          = 1; //
    uint32 TopicId     = 2; // topicid
    uint32 ArticleId   = 3; // 文章id
    uint32 ArticleName = 4; // 文章name
    int64  CreateTime  = 5; // 创建时间
    int64  UpdateTime  = 6; // 修改时间
    int64  DeleteTime  = 7; // 删除时间
}