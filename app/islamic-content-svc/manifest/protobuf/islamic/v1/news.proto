syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";
import "pbentity/news_article.proto";
import "pbentity/news_article_language.proto";
import "pbentity/news_category.proto";
import "pbentity/news_category_language.proto";
import "pbentity/news_topic.proto";
import "pbentity/news_topic_language.proto";
import "common/front_info.proto";
import "common/base.proto";

message NewsCategoryListReq {
string language_id = 1;   // 语言id
string pid = 2;  //父类id
}



message CategoryInfo {
  uint64 category_id = 1;
  uint64 language_id = 2;
  string name = 3;
  pbentity.NewsCategory data = 4;
}

message NewsCategoryListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated CategoryInfo data = 4;
}


message NewsListByCateIdReq {
  string cate_id = 1;   // 分类id
  string language_id = 2;   // 语言id

}
message NewsListByCateIdRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated ArticleInfo data = 4;

}



message NewsTopicListReq {
  string language_id = 1;   // 语言id

}


message TopicInfo {
  uint64 topic_id = 1;               // 用户id
  uint64 language_id = 2;               // 用户id
  string name = 3;          // 账号
  string short_name = 4;          // 账号
  pbentity.NewsTopic data = 5;
}

message NewsTopicListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated TopicInfo data = 4; // 专题信息
}

message NewsListByTopicIdReq {
  string topic_id = 1;   // 话题id
  string language_id = 2;   // 语言id
}



message ArticleInfo {
  uint64 article_id = 1;
  uint64 language_id = 2;
  string name = 3;
  string content = 4;
  pbentity.NewsArticle data = 5;
}

message NewsListByTopicIdRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated ArticleInfo data = 4;
}

service NewsService {
  // 新闻分类列表
rpc NewsCategoryList(NewsCategoryListReq) returns (NewsCategoryListRes);
  // 新闻列表通过分类ID
rpc NewsListByCateId(NewsListByCateIdReq) returns (NewsListByCateIdRes);
//新闻专题列表
rpc NewsTopicList(NewsTopicListReq) returns (NewsTopicListRes);
  // 新闻列表通过专题ID
rpc NewsListByTopicId(NewsListByTopicIdReq) returns (NewsListByTopicIdRes);
}
