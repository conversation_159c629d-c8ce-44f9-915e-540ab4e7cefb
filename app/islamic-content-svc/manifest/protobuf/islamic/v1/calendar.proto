syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";
import "common/base.proto";
import "pbentity/calendar_hijriah.proto";
import "pbentity/calendar_events.proto";

// 日历查询请求
message CalendarReq {
  int32 year = 1;                           // 公历年份
  int32 month = 2;                          // 公历月份
  string method_code = 3;                   // 计算方法：AUTO, LFNU, UMMUL_QURA
  int32 date_adjustment = 4;                // 日期校正：-3到+3天的偏移量
}

// 日历日期信息
message CalendarDateInfo {
  int32 gregorian_year = 1;                 // 公历年
  int32 gregorian_month = 2;                // 公历月
  int32 gregorian_day = 3;                  // 公历日
  int32 hijriah_year = 4;                   // Hijriah年
  int32 hijriah_month = 5;                  // Hijriah月
  int32 hijriah_day = 6;                    // Hijriah日
  string method_code = 7;                   // 计算方法代码
  int32 weekday = 8;                        // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
  int32 pasaran = 9;                        // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
  string weekday_name = 10;                 // 星期名称（本地化）
  string pasaran_name = 11;                 // Pasaran名称（本地化）
  string java_day = 12;                     // Java日历组合（如：Senin Kliwon）
  repeated CalendarEventInfo events = 13;   // 当日事件列表
}

// 日历事件信息
message CalendarEventInfo {
  int64 id = 1;                             // 事件ID
  string event_type = 2;                    // 事件类型：HARI_BESAR, LIBUR_NASIONAL, PUASA
  string title = 3;                         // 事件标题
  string description = 4;                   // 事件描述
  string jump_url = 5;                      // 点击跳转链接
  string event_type_name = 6;               // 事件类型名称（本地化）
}

// 日历响应
message CalendarRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated CalendarDateInfo data = 4;       // 日历数据列表
}

// 事件列表请求
message EventListReq {
  int32 year = 1;                           // 公历年份（可选）
  int32 month = 2;                          // 公历月份（可选）
  string event_type = 3;                    // 事件类型过滤（可选）
  int32 page = 4;                           // 页码，默认1
  int32 page_size = 5;                      // 每页数量，默认20
}

// 事件列表响应
message EventListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated CalendarEventInfo data = 4;      // 事件列表
  int32 total = 5;                          // 总数量
  int32 page = 6;                           // 当前页码
  int32 page_size = 7;                      // 每页数量
}

// Hijriah日期转换请求
message HijriahConvertReq {
  int32 gregorian_year = 1;                 // 公历年
  int32 gregorian_month = 2;                // 公历月
  int32 gregorian_day = 3;                  // 公历日
  string method_code = 4;                   // 计算方法：AUTO, LFNU, UMMUL_QURA
  int32 date_adjustment = 5;                // 日期校正：-3到+3天的偏移量
}

// Hijriah日期转换响应
message HijriahConvertRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  CalendarDateInfo data = 4;                // 转换后的日期信息
}

// 日历设置信息
message CalendarSettingsInfo {
  repeated string available_methods = 1;    // 可用的计算方法列表
  string default_method = 2;                // 默认计算方法
  repeated int32 adjustment_options = 3;    // 可用的日期校正选项
  int32 default_adjustment = 4;             // 默认日期校正
}

// 日历设置请求
message CalendarSettingsReq {
  // 空请求，获取设置信息
}

// 日历设置响应
message CalendarSettingsRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  CalendarSettingsInfo data = 4;            // 设置信息
}

// 日历服务定义
service CalendarService {
  // 获取日历数据
  rpc GetCalendar(CalendarReq) returns (CalendarRes);
  
  // 获取事件列表
  rpc GetEventList(EventListReq) returns (EventListRes);
  
  // Hijriah日期转换
  rpc ConvertHijriah(HijriahConvertReq) returns (HijriahConvertRes);
  
  // 获取日历设置
  rpc GetCalendarSettings(CalendarSettingsReq) returns (CalendarSettingsRes);
}
