syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";
import "common/base.proto";
import "pbentity/calendar_hijriah.proto";
import "pbentity/calendar_events.proto";

// 日历查询请求
message CalendarReq {
  int32 year = 1;                           // 公历年份
  int32 month = 2;                          // 公历月份
  string method_code = 3;                   // 计算方法：AUTO, LFNU, UMMUL_QURA
  int32 date_adjustment = 4;                // 日期校正：-3到+3天的偏移量
}

// 日历日期信息
message CalendarDateInfo {
  int32 gregorian_year = 1;                 // 公历年
  int32 gregorian_month = 2;                // 公历月
  int32 gregorian_day = 3;                  // 公历日
  int32 hijriah_year = 4;                   // Hijriah年
  int32 hijriah_month = 5;                  // Hijriah月 1-12 (<PERSON><PERSON><PERSON>, Safar, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
  int32 hijriah_day = 6;                    // Hijriah日
  string method_code = 7;                   // 计算方法代码
  int32 weekday = 8;                        // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
  int32 pasaran = 9;                        // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
  string weekday_name = 10;                 // 星期名称（本地化）(Ahad Senin Selasa Rabu Kamis Jumat Sabtu)
  string pasaran_name = 11;                 // Pasaran名称（本地化）
  repeated CalendarEventInfo events = 12;   // 当日事件列表
}

// 日历事件信息
message CalendarEventInfo {
  int64 id = 1;                             // 事件ID
  string event_type = 2;                    // 事件类型：HARI_BESAR, LIBUR_NASIONAL, PUASA
  string title = 3;                         // 事件标题
  string description = 4;                   // 事件描述
  string jump_url = 5;                      // 点击跳转链接
  string event_type_name = 6;               // 事件类型名称（本地化）
}

// 日历响应
message CalendarRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  repeated CalendarDateInfo data = 4;       // 日历数据列表
}

// 日历服务定义
service CalendarService {
  // 获取日历数据
  rpc GetCalendar(CalendarReq) returns (CalendarRes);
}
