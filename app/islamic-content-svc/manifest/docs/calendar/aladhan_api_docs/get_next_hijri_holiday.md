## Get Next Hijri holiday

This endpoint returns the next Hijri holiday.

**Endpoint:** `/v1/nextHijriHoliday`

**Method:** GET

### Path Parameters

This endpoint does not take any path parameters.

### Query-String Parameters

- calendarMethod
  A Calendar Calculation Method.
  Defaults to HJCoSA.

  - HJCoSA - High Judicial Council of Saudi Arabia (this is used on aladhan.com)
  - UAQ - Umm al-Qura
  - DIYANET - Diyanet İşleri Başkanlığı
  - MATHEMATICAL

  Default: HJCoSA
  Allowed: HJCoSA ┃ UAQ ┃ DIYANET ┃ MATHEMATICAL

  For more details on the methods, please see https://api.aladhan.com/v1/islamicCalendar/methods.

  Examples: UAQ

### Example Request

```
/v1/nextHijriHoliday
```

### Example JSON Response

```json
{
  "code": 200,
  "status": "OK",
  "data": {
    "hijri": {
      "date": "12-03-1446",
      "format": "DD-MM-YYYY",
      "day": "12",
      "weekday": {
        "en": "<PERSON>-<PERSON>ham<PERSON>",
        "ar": "الخميس"
      },
      "month": {
        "number": 3,
        "en": "Rabi' al-awwal",
        "ar": "ربيع الأول"
      },
      "year": "1446",
      "designation": {
        "abbreviated": "AH",
        "expanded": "Anno Hegirae"
      },
      "holidays": ["Mawlid al-Nabi"]
    },
    "gregorian": {
      "date": "15-09-2024",
      "format": "DD-MM-YYYY",
      "day": "15",
      "weekday": {
        "en": "Sunday"
      },
      "month": {
        "number": 9,
        "en": "September"
      },
      "year": "2024",
      "designation": {
        "abbreviated": "AD",
        "expanded": "Anno Domini"
      }
    }
  }
}
```
