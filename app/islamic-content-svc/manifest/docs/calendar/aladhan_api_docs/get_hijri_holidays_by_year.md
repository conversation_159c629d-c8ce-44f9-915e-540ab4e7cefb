## Get Hijri holidays by year

This endpoint returns a list of Hijri holidays for a specific year.

**Endpoint:** `/v1/hijriHolidays/{year}`

**Method:** GET

### Path Parameters

| Name   | Type    | Description     |
| :----- | :------ | :-------------- |
| `year` | Integer | The Hijri year. |

### Query-String Parameters

- calendarMethod
  A Calendar Calculation Method.
  Defaults to HJCoSA.

  - HJCoSA - High Judicial Council of Saudi Arabia (this is used on aladhan.com)
  - UAQ - Umm al-Qura
  - DIYANET - Diyanet İşleri Başkanlığı
  - MATHEMATICAL

  Default: HJCoSA
  Allowed: HJCoSA ┃ UAQ ┃ DIYANET ┃ MATHEMATICAL

  For more details on the methods, please see https://api.aladhan.com/v1/islamicCalendar/methods.

  Examples: UAQ

### Example Request

```
/v1/hijriHolidays/1446
```

### Example JSON Response

```json
{
  "code": 200,
  "status": "OK",
  "data": [
    {
      "name": "Islamic New Year",
      "date": {
        "hijri": "01-01-1446",
        "gregorian": "07-07-2024"
      }
    },
    {
      "name": "Ashura",
      "date": {
        "hijri": "10-01-1446",
        "gregorian": "16-07-2024"
      }
    }
  ]
}
```
