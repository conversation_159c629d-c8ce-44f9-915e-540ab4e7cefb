## Get a Gregorian calendar for a Hijri month

This endpoint returns a Gregorian calendar for a specific Hijri month.

**Endpoint:** `/v1/hToGCalendar/{month}/{year}`

**Method:** GET

### Path Parameters

| Name    | Type    | Description             |
| :------ | :------ | :---------------------- |
| `month` | Integer | The Hijri month (1-12). |
| `year`  | Integer | The Hijri year.         |

### Query-String Parameters

- calendarMethod
  A Calendar Calculation Method.
  Defaults to HJCoSA.

  - HJCoSA - High Judicial Council of Saudi Arabia (this is used on aladhan.com)
  - UAQ - Umm al-Qura
  - DIYANET - Diyanet İşleri Başkanlığı
  - MATHEMATICAL

  Default: HJCoSA
  Allowed: HJCoSA ┃ UAQ ┃ DIYANET ┃ MATHEMATICAL

  For more details on the methods, please see https://api.aladhan.com/v1/islamicCalendar/methods.

  Examples: UAQ

### Example Request

```
/v1/hToGCalendar/1/1446
```

### Example JSON Response

```json
{
  "code": 200,
  "status": "OK",
  "data": [
    {
      "hijri": {
        "date": "01-01-1446",
        "format": "DD-MM-YYYY",
        "day": "01",
        "weekday": {
          "en": "Al-Ahad",
          "ar": "الأحد"
        },
        "month": {
          "number": 1,
          "en": "Muharram",
          "ar": "المحرم"
        },
        "year": "1446",
        "designation": {
          "abbreviated": "AH",
          "expanded": "Anno Hegirae"
        },
        "holidays": ["Islamic New Year"]
      },
      "gregorian": {
        "date": "07-07-2024",
        "format": "DD-MM-YYYY",
        "day": "07",
        "weekday": {
          "en": "Sunday"
        },
        "month": {
          "number": 7,
          "en": "July"
        },
        "year": "2024",
        "designation": {
          "abbreviated": "AD",
          "expanded": "Anno Domini"
        }
      }
    }
  ]
}
```
