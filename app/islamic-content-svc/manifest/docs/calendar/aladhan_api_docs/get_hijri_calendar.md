## Get a Hijri calendar for a Gregorian month

This endpoint returns a Hijri calendar for a specific Gregorian month.

**Endpoint:** `/v1/gToHCalendar/{month}/{year}`

**Method:** GET

### Path Parameters

| Name    | Type    | Description                 |
| :------ | :------ | :-------------------------- |
| `month` | Integer | The Gregorian month (1-12). |
| `year`  | Integer | The Gregorian year.         |

### Query-String Parameters

- calendarMethod
  A Calendar Calculation Method.
  Defaults to HJCoSA.

  - HJCoSA - High Judicial Council of Saudi Arabia (this is used on aladhan.com)
  - UAQ - Umm al-Qura
  - DIYANET - Diyanet İşleri Başkanlığı
  - MATHEMATICAL

  Default: HJCoSA
  Allowed: HJCoSA ┃ UAQ ┃ DIYANET ┃ MATHEMATICAL

  For more details on the methods, please see https://api.aladhan.com/v1/islamicCalendar/methods.

  Examples: UAQ

### Example Request

```
/v1/gToHCalendar/8/2024
```

### Example JSON Response

```json
{
  "code": 200,
  "status": "OK",
  "data": [
    {
      "hijri": {
        "date": "01-02-1446",
        "format": "DD-MM-YYYY",
        "day": "01",
        "weekday": {
          "en": "Al-Ahad",
          "ar": "الأحد"
        },
        "month": {
          "number": 2,
          "en": "Safar",
          "ar": "صَفَر"
        },
        "year": "1446",
        "designation": {
          "abbreviated": "AH",
          "expanded": "Anno Hegirae"
        },
        "holidays": []
      },
      "gregorian": {
        "date": "04-08-2024",
        "format": "DD-MM-YYYY",
        "day": "04",
        "weekday": {
          "en": "Sunday"
        },
        "month": {
          "number": 8,
          "en": "August"
        },
        "year": "2024",
        "designation": {
          "abbreviated": "AD",
          "expanded": "Anno Domini"
        }
      }
    }
  ]
}
```
