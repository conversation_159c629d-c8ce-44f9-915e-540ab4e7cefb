## Get List of special days

This endpoint returns a list of special days in the Hijri calendar.

**Endpoint:** `/v1/specialDays`

**Method:** GET

### Path Parameters

This endpoint does not take any path parameters.

### Query-String Parameters

This endpoint does not take any query-string parameters.

### Example Request

```
/v1/specialDays
```

### Example JSON Response

```json
{
  "code": 200,
  "status": "OK",
  "data": [
    {
      "name": "Islamic New Year",
      "date": {
        "hijri": "01-01",
        "gregorian": "07-07"
      }
    },
    {
      "name": "<PERSON><PERSON>",
      "date": {
        "hijri": "10-01",
        "gregorian": "16-07"
      }
    }
  ]
}
```