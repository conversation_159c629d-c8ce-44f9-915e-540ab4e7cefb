## Get Holiday for a specific Hijri day

This endpoint returns the holiday for a specific Hijri day.

**Endpoint:** `/v1/hijriHoliday/{date}`

**Method:** GET

### Path Parameters

| Name   | Type   | Description                         |
| :----- | :----- | :---------------------------------- |
| `date` | String | The Hijri date in DD-MM-YYYY format. |

### Query-String Parameters

This endpoint does not take any query-string parameters.

### Example Request

```
/v1/hijriHoliday/09-12-1446
```

### Example JSON Response

```json
{
  "code": 200,
  "status": "OK",
  "data": [
    {
      "hijri": {
        "date": "09-12-1446",
        "format": "DD-MM-YYYY",
        "day": "09",
        "weekday": {
          "en": "Al-Jumu'ah",
          "ar": "الجمعة"
        },
        "month": {
          "number": 12,
          "en": "Dhu al-Hijjah",
          "ar": "ذو الحجة"
        },
        "year": "1446",
        "designation": {
          "abbreviated": "AH",
          "expanded": "Anno Hegirae"
        },
        "holidays": [
          "Day of Arafah"
        ]
      },
      "gregorian": {
        "date": "06-06-2025",
        "format": "DD-MM-YYYY",
        "day": "06",
        "weekday": {
          "en": "Friday"
        },
        "month": {
          "number": 6,
          "en": "June"
        },
        "year": "2025",
        "designation": {
          "abbreviated": "AD",
          "expanded": "Anno Domini"
        }
      }
    }
  ]
}
```