## Get Islamic year from gregorian year

This endpoint returns the Islamic year for a given Gregorian year.

**Endpoint:** `/v1/islamicYear/{year}`

**Method:** GET

### Path Parameters

| Name   | Type    | Description        |
| :----- | :------ | :----------------- |
| `year` | Integer | The Gregorian year. |

### Query-String Parameters

This endpoint does not take any query-string parameters.

### Example Request

```
/v1/islamicYear/2025
```

### Example JSON Response

```json
{
  "code": 200,
  "status": "OK",
  "data": 1447
}
```