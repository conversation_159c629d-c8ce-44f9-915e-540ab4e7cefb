## Get Islamic months

This endpoint returns a list of Islamic months.

**Endpoint:** `/v1/islamicMonths`

**Method:** GET

### Path Parameters

This endpoint does not take any path parameters.

### Query-String Parameters

This endpoint does not take any query-string parameters.

### Example Request

```
/v1/islamicMonths
```

### Example JSON Response

```json
{
  "code": 200,
  "status": "OK",
  "data": [
    {
      "number": 1,
      "en": "<PERSON><PERSON><PERSON>",
      "ar": "المحرم"
    },
    {
      "number": 2,
      "en": "Safar",
      "ar": "صَفَر"
    }
  ]
}
```