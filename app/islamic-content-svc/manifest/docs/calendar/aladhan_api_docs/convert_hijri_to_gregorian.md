## Convert a Hijri date to a Gregorian date

This endpoint converts a Hijri date to a Gregorian date.

**Endpoint:** `/v1/hToG/{date}`

**Method:** GET

### Path Parameters

| Name   | Type   | Description                          |
| :----- | :----- | :----------------------------------- |
| `date` | String | The Hijri date in DD-MM-YYYY format. |

### Query-String Parameters

- calendarMethod
  A Calendar Calculation Method.
  Defaults to HJCoSA.

  - HJCoSA - High Judicial Council of Saudi Arabia (this is used on aladhan.com)
  - UAQ - Umm al-Qura
  - DIYANET - Diyanet İşleri <PERSON>lığı
  - MATHEMATICAL

  Default: HJCoSA
  Allowed: HJCoSA ┃ UAQ ┃ DIYANET ┃ MATHEMATICAL

  For more details on the methods, please see https://api.aladhan.com/v1/islamicCalendar/methods.

  Examples: UAQ

### Example Request

```
/v1/hToG/01-01-1447
```

### Example JSON Response

```json
{
  "code": 200,
  "status": "OK",
  "data": {
    "hijri": {
      "date": "01-01-1447",
      "format": "DD-MM-YYYY",
      "day": "01",
      "weekday": {
        "en": "Al-Ahad",
        "ar": "الأحد"
      },
      "month": {
        "number": 1,
        "en": "Muharram",
        "ar": "المحرم"
      },
      "year": "1447",
      "designation": {
        "abbreviated": "AH",
        "expanded": "Anno Hegirae"
      },
      "holidays": ["Islamic New Year"]
    },
    "gregorian": {
      "date": "29-07-2025",
      "format": "DD-MM-YYYY",
      "day": "29",
      "weekday": {
        "en": "Tuesday"
      },
      "month": {
        "number": 7,
        "en": "July"
      },
      "year": "2025",
      "designation": {
        "abbreviated": "AD",
        "expanded": "Anno Domini"
      }
    }
  }
}
```
