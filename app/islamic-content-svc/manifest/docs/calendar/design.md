# 基本需求

前端要做一个 calendar 的功能，产品文档在 app/islamic-content-svc/manifest/docs/calendar/日历-ok.html。
目前可用的 api 接口在 app/islamic-content-svc/manifest/docs/calendar/aladhan_api_docs/目录下。
<PERSON>sar & Libur Nasional 和 Puasa 部分，除了类型不同，还需要支持点击跳转。然后这两部分的内容，除了通过爬虫获取数据，还可以通过人工添加数据。爬虫部分先不用着急实现。

# 其他概念

## Hijriah 历法设置（Pengaturan Kalender Hijriah）

- 方法选择（Metode Perhitungan）支持 Lembaga Falakiyah NU， Ummul Qura。但是 Lembaga Falakiyah NU 应该是要爬数据的，但是我目前没有找到，所以就先给我建表吧，以公历 2025-06-24+LFNU 这种作为“唯一性”吧。
  而 Ummul Qura，是可以通过计算获取的。参考一下例子：

  ```go
  package main

  import (
      "fmt"
      "time"

      "github.com/dromara/carbon/v2"
      "github.com/salsowelim/go-hijri"
  )

  func main() {

      var curYear = 2024
      for month := 1; month <= 12; month++ {
          monthDate := carbon.CreateFromDate(curYear, month, 1)
          daysInMonth := monthDate.DaysInMonth()
          fmt.Printf("month: %d, daysInMonth: %d\n", month, daysInMonth)
          for day := 1; day <= daysInMonth; day++ {
              cc := monthDate.SetDay(day)
              y, m, d, _ := hijri.ToUmmAlQura(cc.StdTime())
              fmt.Printf("%s AD = %04d-%02d-%02d H (standard)， Java day: %s\n", cc.ToDateString(), y, m, d, getPasaran(cc.StdTime()))
          }
      }
  }

  func getPasaran(date time.Time) string {
      // 基准日期：1968-12-03 是 Slasa Kliwon (JDN = 2440213)
      baseDate := time.Date(1968, 12, 3, 0, 0, 0, 0, time.UTC)
      // baseJDN := 2440213 // 通过 Meeus 算法或工具计算
      basePasaran := 0 // Kliwon

      // 计算目标日期的 JDN（简化为天数差）
      daysDiff := int(date.Sub(baseDate).Hours() / 24)
      pasaranIndex := (basePasaran + daysDiff) % 5
      if pasaranIndex < 0 {
          pasaranIndex += 5
      }

      pasaranNames := []string{"Kliwon", "Legi", "Pahing", "Pon", "Wage"}
      weekdayNames := []string{"Minggu", "Senin", "Selasa", "Rebo", "Kemis", "Jumat", "Setu"}

      // 获取星期
      weekday := int(date.Weekday())
      return fmt.Sprintf("%s %s", weekdayNames[weekday], pasaranNames[pasaranIndex])
  }

  ```

还有什么需要讨论的吗？如果没有就先给我创建 sql 表吧。在 app/islamic-content-svc/manifest/sql/目录下，只需要放在同一个文件就行
