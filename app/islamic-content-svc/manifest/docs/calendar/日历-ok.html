<!DOCTYPE html>
<!-- saved from url=(0070)https://dkhtg1.axshare.com/?id=uboha9&p=%E6%97%A5%E5%8E%86-ok&c=1&sc=3 -->
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><style>.ͼ1.cm-focused {outline: 1px dotted #212121;}
.ͼ1 {position: relative !important; box-sizing: border-box; display: flex !important; flex-direction: column;}
.ͼ1 .cm-scroller {display: flex !important; align-items: flex-start !important; font-family: monospace; line-height: 1.4; height: 100%; overflow-x: auto; position: relative; z-index: 0; overflow-anchor: none;}
.ͼ1 .cm-content[contenteditable=true] {-webkit-user-modify: read-write-plaintext-only;}
.ͼ1 .cm-content {margin: 0; flex-grow: 2; flex-shrink: 0; display: block; white-space: pre; word-wrap: normal; box-sizing: border-box; min-height: 100%; padding: 4px 0; outline: none;}
.ͼ1 .cm-lineWrapping {white-space: pre-wrap; white-space: break-spaces; word-break: break-word; overflow-wrap: anywhere; flex-shrink: 1;}
.ͼ2 .cm-content {caret-color: black;}
.ͼ3 .cm-content {caret-color: white;}
.ͼ1 .cm-line {display: block; padding: 0 2px 0 6px;}
.ͼ1 .cm-layer > * {position: absolute;}
.ͼ1 .cm-layer {position: absolute; left: 0; top: 0; contain: size style;}
.ͼ2 .cm-selectionBackground {background: #d9d9d9;}
.ͼ3 .cm-selectionBackground {background: #222;}
.ͼ2.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #d7d4f0;}
.ͼ3.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #233;}
.ͼ1 .cm-cursorLayer {pointer-events: none;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer {animation: steps(1) cm-blink 1.2s infinite;}
@keyframes cm-blink {50% {opacity: 0;}}
@keyframes cm-blink2 {50% {opacity: 0;}}
.ͼ1 .cm-cursor, .ͼ1 .cm-dropCursor {border-left: 1.2px solid black; margin-left: -0.6px; pointer-events: none;}
.ͼ1 .cm-cursor {display: none;}
.ͼ3 .cm-cursor {border-left-color: #ddd;}
.ͼ1 .cm-dropCursor {position: absolute;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor {display: block;}
.ͼ1 .cm-iso {unicode-bidi: isolate;}
.ͼ1 .cm-announced {position: fixed; top: -10000px;}
@media print {.ͼ1 .cm-announced {display: none;}}
.ͼ2 .cm-activeLine {background-color: #cceeff44;}
.ͼ3 .cm-activeLine {background-color: #99eeff33;}
.ͼ2 .cm-specialChar {color: red;}
.ͼ3 .cm-specialChar {color: #f78;}
.ͼ1 .cm-gutters {flex-shrink: 0; display: flex; height: 100%; box-sizing: border-box; z-index: 200;}
.ͼ1 .cm-gutters-before {inset-inline-start: 0;}
.ͼ1 .cm-gutters-after {inset-inline-end: 0;}
.ͼ2 .cm-gutters.cm-gutters-before {border-right-width: 1px;}
.ͼ2 .cm-gutters.cm-gutters-after {border-left-width: 1px;}
.ͼ2 .cm-gutters {background-color: #f5f5f5; color: #6c6c6c; border: 0px solid #ddd;}
.ͼ3 .cm-gutters {background-color: #333338; color: #ccc;}
.ͼ1 .cm-gutter {display: flex !important; flex-direction: column; flex-shrink: 0; box-sizing: border-box; min-height: 100%; overflow: hidden;}
.ͼ1 .cm-gutterElement {box-sizing: border-box;}
.ͼ1 .cm-lineNumbers .cm-gutterElement {padding: 0 3px 0 5px; min-width: 20px; text-align: right; white-space: nowrap;}
.ͼ2 .cm-activeLineGutter {background-color: #e2f2ff;}
.ͼ3 .cm-activeLineGutter {background-color: #222227;}
.ͼ1 .cm-panels {box-sizing: border-box; position: sticky; left: 0; right: 0; z-index: 300;}
.ͼ2 .cm-panels {background-color: #f5f5f5; color: black;}
.ͼ2 .cm-panels-top {border-bottom: 1px solid #ddd;}
.ͼ2 .cm-panels-bottom {border-top: 1px solid #ddd;}
.ͼ3 .cm-panels {background-color: #333338; color: white;}
.ͼ1 .cm-dialog label {font-size: 80%;}
.ͼ1 .cm-dialog {padding: 2px 19px 4px 6px; position: relative;}
.ͼ1 .cm-dialog-close {position: absolute; top: 3px; right: 4px; background-color: inherit; border: none; font: inherit; font-size: 14px; padding: 0;}
.ͼ1 .cm-tab {display: inline-block; overflow: hidden; vertical-align: bottom;}
.ͼ1 .cm-widgetBuffer {vertical-align: text-top; height: 1em; width: 0; display: inline;}
.ͼ1 .cm-placeholder {color: #888; display: inline-block; vertical-align: top; user-select: none;}
.ͼ1 .cm-highlightSpace {background-image: radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%); background-position: center;}
.ͼ1 .cm-highlightTab {background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>'); background-size: auto 100%; background-position: right 90%; background-repeat: no-repeat;}
.ͼ1 .cm-trailingSpace {background-color: #ff332255;}
.ͼ1 .cm-button {vertical-align: middle; color: inherit; font-size: 70%; padding: .2em 1em; border-radius: 1px;}
.ͼ2 .cm-button:active {background-image: linear-gradient(#b4b4b4, #d0d3d6);}
.ͼ2 .cm-button {background-image: linear-gradient(#eff1f5, #d9d9df); border: 1px solid #888;}
.ͼ3 .cm-button:active {background-image: linear-gradient(#111, #333);}
.ͼ3 .cm-button {background-image: linear-gradient(#393939, #111); border: 1px solid #888;}
.ͼ1 .cm-textfield {vertical-align: middle; color: inherit; font-size: 70%; border: 1px solid silver; padding: .2em .5em;}
.ͼ2 .cm-textfield {background-color: white;}
.ͼ3 .cm-textfield {border: 1px solid #555; background-color: inherit;}
.ͼ15 .cm-focused {outline: none;}
.ͼp {color: #c678dd;}
.ͼq {color: #e06c75;}
.ͼr {color: #61afef;}
.ͼs {color: #d19a66;}
.ͼt {color: #abb2bf;}
.ͼu {color: #e5c07b;}
.ͼv {color: #56b6c2;}
.ͼw {color: #7d8799;}
.ͼx {font-weight: bold;}
.ͼy {font-style: italic;}
.ͼz {text-decoration: line-through;}
.ͼ10 {color: #7d8799; text-decoration: underline;}
.ͼ11 {font-weight: bold; color: #e06c75;}
.ͼ12 {color: #d19a66;}
.ͼ13 {color: #98c379;}
.ͼ14 {color: #ffffff;}
.ͼo {color: #abb2bf; background-color: #282c34;}
.ͼo .cm-content {caret-color: #528bff;}
.ͼo .cm-cursor, .ͼo .cm-dropCursor {border-left-color: #528bff;}
.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .ͼo .cm-selectionBackground, .ͼo .cm-content ::selection {background-color: #3E4451;}
.ͼo .cm-panels {background-color: #21252b; color: #abb2bf;}
.ͼo .cm-panels.cm-panels-top {border-bottom: 2px solid black;}
.ͼo .cm-panels.cm-panels-bottom {border-top: 2px solid black;}
.ͼo .cm-searchMatch {background-color: #72a1ff59; outline: 1px solid #457dff;}
.ͼo .cm-searchMatch.cm-searchMatch-selected {background-color: #6199ff2f;}
.ͼo .cm-activeLine {background-color: #6699ff0b;}
.ͼo .cm-selectionMatch {background-color: #aafe661a;}
.ͼo.cm-focused .cm-matchingBracket, .ͼo.cm-focused .cm-nonmatchingBracket {background-color: #bad0f847;}
.ͼo .cm-gutters {background-color: #282c34; color: #7d8799; border: none;}
.ͼo .cm-activeLineGutter {background-color: #2c313a;}
.ͼo .cm-foldPlaceholder {background-color: transparent; border: none; color: #ddd;}
.ͼo .cm-tooltip {border: none; background-color: #353a42;}
.ͼo .cm-tooltip .cm-tooltip-arrow:before {border-top-color: transparent; border-bottom-color: transparent;}
.ͼo .cm-tooltip .cm-tooltip-arrow:after {border-top-color: #353a42; border-bottom-color: #353a42;}
.ͼo .cm-tooltip-autocomplete > ul > li[aria-selected] {background-color: #2c313a; color: #abb2bf;}
.ͼ5 {color: #404740;}
.ͼ6 {text-decoration: underline;}
.ͼ7 {text-decoration: underline; font-weight: bold;}
.ͼ8 {font-style: italic;}
.ͼ9 {font-weight: bold;}
.ͼa {text-decoration: line-through;}
.ͼb {color: #708;}
.ͼc {color: #219;}
.ͼd {color: #164;}
.ͼe {color: #a11;}
.ͼf {color: #e40;}
.ͼg {color: #00f;}
.ͼh {color: #30a;}
.ͼi {color: #085;}
.ͼj {color: #167;}
.ͼk {color: #256;}
.ͼl {color: #00c;}
.ͼm {color: #940;}
.ͼn {color: #f00;}
.ͼ4 .cm-line ::selection, .ͼ4 .cm-line::selection {background-color: transparent !important;}
.ͼ4 .cm-line {caret-color: transparent !important;}
.ͼ4 .cm-content :focus::selection, .ͼ4 .cm-content :focus ::selection {background-color: Highlight !important;}
.ͼ4 .cm-content :focus {caret-color: initial !important;}
.ͼ4 .cm-content {caret-color: transparent !important;}
</style>
    <meta name="robots" content="noindex, nofollow">
   <link href="./日历-ok_files/css2" rel="stylesheet">
    <title>日历-ok</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <link type="text/css" href="./日历-ok_files/reset.css" rel="Stylesheet">
    <link type="text/css" href="./日历-ok_files/default.css" rel="Stylesheet">
    
    
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link href="./日历-ok_files/css" rel="stylesheet">
    <link href="./日历-ok_files/css2(1)" rel="stylesheet">

    <link rel="icon" href="https://app.axure.cloud/app/favicon.ico"><meta property="og:site_name" content="Axure Cloud">
<meta property="og:type" content="website">
<meta property="og:title" content="Halal-app - 日历-ok">
<meta property="og:description" content="Created with Axure RP">
<meta property="og:url" content="https://dkhtg1.axshare.com/?id=uboha9">
<meta property="og:image" content="https://files.axshare.com/gsc/DKHTG1/thumbnails/uboha9.png">
<link rel="alternate" type="application/json+oembed" href="https://app.axure.cloud/oembed?url=https%3A%2F%2Fdkhtg1.axshare.com%2F%3Fid%3Duboha9" title="oEmbed"><script type="text/javascript">
        AXSHARE_HOST_URL = 'http://app.axure.cloud';
        AXSHARE_HOST_SECURE_URL = 'https://app.axure.cloud';
        ACCOUNT_SERVICE_URL = 'http://accounts.axure.com';
        ACCOUNT_SERVICE_SECURE_URL = 'https://accounts.axure.com';
        ON_PREM_LDAP_ENABLED = 'false';
        AXSHARE_CLIENT_URL = 'https://app.axure.cloud/app'
</script><script type="text/javascript">
        if (location.href.toString().indexOf('file://localhost/') == 0) {
            location.href = location.href.toString().replace('file://localhost/', 'file:///');
        }
    </script>
    <!--<link type="text/css" rel="Stylesheet" href="plugins/sitemap/styles/sitemap.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/page_notes/styles/page_notes.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/debug/styles/debug.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/handoff/styles/handoff.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/handoff/styles/codemirror.css" />-->
<script type="text/javascript" async="" src="./日历-ok_files/document.js"></script><script type="text/javascript" src="./日历-ok_files/debug.js"></script><script type="text/javascript" src="./日历-ok_files/sitemap.js"></script><script type="text/javascript" src="./日历-ok_files/page_notes.js"></script><script type="text/javascript" src="./日历-ok_files/hintmanager.js"></script><link type="text/css" rel="stylesheet" href="./日历-ok_files/debug.css"><link type="text/css" rel="stylesheet" href="./日历-ok_files/sitemap.css"><link type="text/css" rel="stylesheet" href="./日历-ok_files/page_notes.css"><script text="text/javascript" language="JavaScript" src="./日历-ok_files/feedback9.js"></script><link rel="stylesheet" type="text/css" href="./日历-ok_files/css2(2)"><link rel="stylesheet" type="text/css" href="./日历-ok_files/feedback9.css"><link text="text/css" href="./日历-ok_files/axImgAreaSelect.css" rel="Stylesheet"></head>
<body scroll="no" class="hashover" style="overflow: hidden;"><div data-v-app=""><div class="feedbackRoot"><!----><!----></div></div>
    <div id="topPanel" style="display: none;">
        <div id="interfaceControlFrame" style="opacity: 1;">
            <div id="interfaceControlFrameLeft">
                <div id="interfaceControlFrameMinimizeContainer" class="">
                    <a title="Collapse" id="interfaceControlFrameMinimizeButton">
                        <div id="minimizeArrow" class="minimizeButtonHover minimizeIcon"></div>
                        <div id="minimizeX" class="minimizeButton minimizeIcon"></div>
                    </a>
                </div>
                <div id="interfaceControlFrameCloseContainer">
                    <a title="Close" id="interfaceControlFrameCloseButton">CLOSE</a>
                </div>

                <div id="sitemapControlFrameContainer" title="Project Pages">
                    <div id="projectControlFrameHeaderContainer">
                        <ul id="projectControlFrameHeader"><li id="sitemapHostBtn"><a pluginid="sitemapHost" title="Project Pages">&nbsp;</a></li></ul>
                    </div>

                    <div id="interfacePageNameContainer">
                        <div id="interfacePageNameButton" class="pageNameHeader">日历-ok</div>
                        <div id="interfacePageCount" class="pageCountHeader">(13 of 60)</div>
                    </div>
                </div>


                <div id="interfaceAdaptiveViewsContainer" title="Adaptive Views" style="display: none;">
                    <div id="interfaceAdaptiveViewsIconButton">&nbsp;</div>
                    <div id="interfaceAdaptiveViewsButton" class="adaptiveViewHeader"> (3368 x any)</div>
                    <div id="adaptiveViewsDropdown" class="caret"></div>
                </div>
            </div>

            <div id="interfaceControlFrameContainer">
                <div id="handoffControlFrameHeaderContainer">
                    <ul id="handoffControlFrameHeader"><li id="handoffPreviewHeaderItem" class="selected"><button id="handoffPreviewBtn" class="handoffHeaderBtn selected">
      <div id="handoffPreviewIcon" class="handoffHeaderIcon"></div>
      <span>Preview</span>
    </button></li><li id="handoffInspectHeaderItem"><button id="handoffInspectBtn" class="handoffHeaderBtn">
      <div id="handoffInspectIcon" class="handoffHeaderIcon"></div>
      <span>Inspect</span>
    </button></li></ul>
                </div>
                <div id="interfaceControlFrameLogoContainer">
                    <!--<div id="previewNotice">
                        Local Preview
                    </div>-->
                </div>
            </div>

            <div id="interfaceControlFrameRight">
                <div id="publishContainerCloud" data-v-app=""><button type="button" class="inline-flex items-center justify-center gap-x-2 whitespace-nowrap rounded-sm leading-6 tracking-btn outline-offset-2 focus-visible:outline-1 data-disabled:pointer-events-none data-disabled:opacity-50 font-medium text-white bg-blue-600 data-[state=open]:bg-blue-800 dark:bg-blue-400 dark:data-[state=open]:bg-blue-500 hover:bg-blue-800 dark:hover:bg-blue-500 focus-visible:outline-indigo-600 dark:focus-visible:outline-slate-100 data-active:text-blue-600 dark:data-active:text-blue-400 data-active:bg-sky-100 dark:data-active:bg-sky-200 data-active:hover:bg-sky-200 dark:data-active:hover:bg-sky-100 h-7 px-2 mr-2" id="reka-popover-trigger-v-0" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed"> Share </button><!----></div>

                <div id="scaleMenuButton" title="Zoom and Scale">
                    <div id="scaleValue">89%</div>
                    <div id="expandScaleArrow" class="caret"></div>
                </div>

                <div id="inspectControlFrameHeaderContainer">
                    <ul id="inspectControlFrameHeader">
                        <li id="overflowBtn">
                            <a id="overflowMenuButton" title="Options"></a>
                        </li>
                    <li id="pageNotesHostBtn"><a pluginid="pageNotesHost" title="Notes"><span></span></a></li><li id="handoffHostBtn" style="display: none"><a pluginid="handoffHost" title="Inspect"><span></span></a></li><li id="feedbackHostBtn"><a pluginid="feedbackHost" title="Comments"><span></span><div class="relative"><!----></div></a></li></ul>
                </div>

                <div id="separatorContainer" class="hasLeft">
                    <div class="separator"></div>
                </div>

                <div id="overflowMadeWith"><a href="https://www.axure.com/" id="axureLogo" target="_blank"></a></div>

            </div>

        </div>
    </div>
    <div id="popupContainer">
        <div id="interfaceAdaptiveViewsListContainer" style="display: none;"><div class="adaptiveViewOption" val="auto"><div class="adapViewRadioButton selectedRadioButton"></div>Adaptive</div><div class="adaptiveViewOption currentAdaptiveView" val="default" data-dim="3368x0" cursor="1"><div class="adapViewRadioButton"></div> (3368 x any)</div></div>

        

        <div id="overflowMenuContainer"><div id="showCommentsOption" class="showOption" style="order: 2"><div class="overflowOptionCheckbox selected"></div>Show comments</div><div id="showNotesOption" class="showOption" style="order: 3"><div class="overflowOptionCheckbox selected"></div>Show note markers</div>
        <div id="showHotspotsOption" class="showOption" style="order: 1"><div class="overflowOptionCheckbox"></div>Show hotspots</div></div>

        <div id="scaleMenuContainer">
        <div class="vpZoomValue" val="25"><div class="zoomValue">25%</div></div><div class="vpZoomValue" val="50"><div class="zoomValue">50%</div></div><div class="vpZoomValue" val="75"><div class="zoomValue">75%</div></div><div class="vpZoomValue" val="100"><div class="zoomValue">100%</div></div><div class="vpZoomValue" val="150"><div class="zoomValue">150%</div></div><div class="vpZoomValue" val="200"><div class="zoomValue">200%</div></div><div class="vpZoomValue" val="250"><div class="zoomValue">250%</div></div><div class="vpZoomValue" val="300"><div class="zoomValue">300%</div></div><div class="vpZoomValue" val="400"><div class="zoomValue">400%</div></div><div id="interfaceScaleListContainer" style="display: block;">
        <div class="vpScaleOption" val="0"><div class="scaleRadioButton"></div>Default scale</div><div class="vpScaleOption" val="1"><div class="scaleRadioButton"></div>Scale to width</div><div class="vpScaleOption" val="2"><div class="scaleRadioButton"></div>Scale to fit</div><div class="vpScaleOption" val="3" hidden="" style="display: block;"><div class="scaleRadioButton selectedRadioButton"></div>User scale</div></div></div>
    </div>
    <div id="outerContainer" style="height: 961px; width: 1854px;"><div id="sitemapHost" class="leftPanel" style="display: none;"><div id="sitemapHeader" '="" class="sitemapHeader"><div id="sitemapToolbar" class="sitemapToolbar"><div class="toolbarRow"><div class="pluginNameHeader">Halal-app</div><div class="leftArrow sitemapToolbarButton"></div><div class="rightArrow sitemapToolbarButton"></div></div><div class="toolbarRow searchBar"><div id="searchDiv"><span id="searchIcon"></span><input id="searchBox" type="text" placeholder="Search" class="searchBoxHint"><span id="clearSearchIcon"></span></div><div id="expandCollapseAll" class="sitemapToolbarButton expanded"></div></div></div></div><div id="sitemapTreeContainer"><ul class="sitemapTree" style="clear:both;"><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:9px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">安装后启动</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:36px"><a class="sitemapPageLink" nodeurl="启动授权提示-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">启动授权提示-ok</span></a></div></div></li></ul></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:9px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">未登录状态</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:36px"><a class="sitemapPageLink" nodeurl="注册_登录-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">注册/登录-ok</span></a></div></div></li></ul></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:9px"><span class="sitemapMinus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">已登录状态</span></div></div><ul style=""><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:26px"><span class="sitemapMinus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">首页</span></div></div><ul style=""><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="首页-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">首页-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="搜索-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">搜索-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="扫一扫_.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">扫一扫*</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="收付款_.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">收付款*</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="消息提醒-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">消息提醒-ok</span></a></div></div></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:43px"><span class="sitemapMinus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">快捷键区-支持6个快捷板块</span></div></div><ul style=""><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="慈善_.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">慈善*</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="古兰经-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">古兰经-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="商城_.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">商城*</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="资讯-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">资讯-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="礼拜时间表-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">礼拜时间表-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover sitemapHighlight"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="日历-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">日历-ok</span></a></div></div></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:60px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="更多-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">更多-ok</span></a></div></div><ul><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:77px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">麦加朝圣-ok</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:104px"><a class="sitemapPageLink" nodeurl="朝觐-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">朝觐-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:104px"><a class="sitemapPageLink" nodeurl="副朝-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">副朝-ok</span></a></div></div></li></ul></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:87px"><a class="sitemapPageLink" nodeurl="斋月-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">斋月-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:87px"><a class="sitemapPageLink" nodeurl="念珠-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">念珠-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:87px"><a class="sitemapPageLink" nodeurl="名言-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">名言-ok</span></a></div></div></li></ul></li></ul></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="首屏广告-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">首屏广告-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="头条-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">头条-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="专题-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">专题-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="推荐内容-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">推荐内容-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="video-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">video-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="热门内容-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">热门内容-ok</span></a></div></div></li></ul></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:26px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">信仰</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="默认页-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">默认页-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="古兰经-ok_1.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">古兰经-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="祷词诵读_祷告-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">祷词诵读&amp;祷告-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="礼拜时间表-ok_1.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">礼拜时间表-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="朝拜方向-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">朝拜方向-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="祷告词与雅辛-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">祷告词与雅辛-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="名言-ok_1.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">名言-ok</span></a></div></div></li></ul></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:36px"><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">财富</span></div></div></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:26px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">我的-ok</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="默认页-ok_1.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">默认页-ok</span></a></div></div></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:43px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="设置-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">设置-ok</span></a></div></div><ul><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="阅读模式-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">阅读模式-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="主题-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">主题-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="礼拜时间设置-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">礼拜时间设置-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="礼拜提醒-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">礼拜提醒-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="文章设置-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">文章设置-ok</span></a></div></div></li></ul></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:43px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="个人资料-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">个人资料-ok</span></a></div></div><ul><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="头像-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">头像-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="昵称-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">昵称-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="全名-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">全名-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="性别-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">性别-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="电话-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">电话-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="我的地址-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">我的地址-ok</span></a></div></div></li></ul></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="nu身份认证-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">NU身份认证-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="message-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">message-ok</span></a></div></div></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:43px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="bookmark-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">bookmark-ok</span></a></div></div><ul><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="ayat_al-quran-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">Ayat Al-Quran-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="wirid___doa-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">Wirid &amp; Doa-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="kegiatan-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">Kegiatan-ok</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="artikel-ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">Artikel-ok</span></a></div></div></li></ul></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="bank-.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">bank-</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="about_-_ok.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">about - ok</span></a></div></div></li></ul></li></ul></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:9px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">接口文档</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:36px"><a class="sitemapPageLink" nodeurl="日历相关.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">日历相关</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:36px"><a class="sitemapPageLink" nodeurl="赞诵词（wirid）与祷告文（doa）.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">赞诵词（Wirid）与祷告文（Doa）</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:36px"><a class="sitemapPageLink" nodeurl="礼拜时间.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">礼拜时间</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:36px"><a class="sitemapPageLink" nodeurl="朝拜方向.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">朝拜方向</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:36px"><a class="sitemapPageLink" nodeurl="塔哈利与雅辛.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">塔哈利与雅辛</span></a></div></div></li></ul></li></ul></div><div id="changePageInstructions" class="pageSwapInstructions">Use  <span class="backKeys"></span>  and  <span class="forwardKeys"></span>  keys<br>to move between pages</div></div>
        

        <div id="mHideSidebar"></div>
        
        <div id="mainPanel" style="height: 961px; opacity: 1; width: 1920px;">
            <div id="mainPanelContainer" style="position: relative; width: 112.36%; height: 1079.78px; transform: scale(0.89); transform-origin: 0px 0px;" data-page-dimensions-type="web" data-scale-n="3">
                <div id="clipFrameScroll" style="position: relative; width: 100%; height: 1079.78px; background-color: rgb(255, 255, 255);">
                    <iframe id="mainFrame" name="mainFrame" width="100%" height="100%" src="./日历-ok_files/saved_resource.html" frameborder="0" style="display: block; position: absolute; width: 100%; height: 1079.78px;" webkitallowfullscreen="" mozallowfullscreen="" allowfullscreen=""></iframe>
                <div id="handoffViewport"><div id="handoffMarkupContainer" style="display: none; pointer-events: none;">
  <div id="handoffOver" class="handoffOver" style="display: none;"></div>
  <div id="handoffOverT" class="handoffOver handoffOverGuide handoffOverGuideV" style="display: none;">
    <div id="handoffOverTValue" class="handoffOverGuideValue"></div>
  </div>
  <div id="handoffOverR" class="handoffOver handoffOverGuide handoffOverGuideH" style="display: none;">
    <div id="handoffOverRValue" class="handoffOverGuideValue"></div>
  </div>
  <div id="handoffOverB" class="handoffOver handoffOverGuide handoffOverGuideV" style="display: none;">
    <div id="handoffOverBValue" class="handoffOverGuideValue"></div>
  </div>
  <div id="handoffOverL" class="handoffOver handoffOverGuide handoffOverGuideH" style="display: none;">
    <div id="handoffOverLValue" class="handoffOverGuideValue"></div>
  </div>
  <div id="handoffSelected" class="handoffSelected" style="display: none;">
    <div id="handoffSelectedWidth" class="handoffDim"></div>
    <div id="handoffSelectedHeight" class="handoffDim"></div>
  </div>
  <div id="handoffSelectedV" class="handoffSelected handoffSelectedGuide" style="display: none;"></div>
  <div id="handoffSelectedH" class="handoffSelected handoffSelectedGuide" style="display: none;"></div>
</div></div></div>
            <!----><!----></div>
        </div><div id="clippingBounds" style="z-index: auto; height: 961px; left: 0px; width: 1854px; opacity: 1;"><div id="notesOverlay">&nbsp;</div>
            <div id="clippingBoundsScrollContainer" style="left: 0.0027px; top: 0px;"><div><!----></div></div>
        </div>

        
    <div id="pageNotesHost" class="rightPanel" style="display: none;"><div class="closeButtonContainer"><button></button></div><div id="pageNotesHeader"><div class="pluginNameHeader">Widget notes</div></div><div id="pageNotesScrollContainer"><div id="pageNotesContainer"><div id="pageNotesEmptyState" class="emptyStateContainer"><div class="emptyStateTitle">No notes</div><div class="emptyStateContent">Notes added in Axure RP will appear here</div></div><span id="pageNotesContent"></span></div></div></div><div id="handoffHost" class="rightPanel" style="display: none;"><div class="closeButtonContainer"><button></button></div><div id="handoffScrollContainer">
  <div id="handoffContainer">
    <div id="handoffModeLinkContainer" class="handoffLinksContainer">
      <div class="dottedDivider"></div>
    </div>
  <div id="handoffProjectInfo" class="handoffLayerSection" style="">
<div id="handoffProjectName" class="handoffSectionName handoffLayerValue"></div><div class="handoff-inspect-header">Inspect</div>
  <div class="handoff-call-to-action">
    <p>← Click on a layer in your prototype to view dimensions &amp; colors, download assets and copy CSS</p>
  </div></div><div id="handoffArtboardColors" style="display: none;"><div class="handoffLayerViewRow">
  <div class="handoffLayerViewCell">
    <span class="handoffCloseSection handoffCloseIcon">×</span>
  </div>
</div><div class="handoffLayerSection"><div class="handoffSectionName">Colors</div><div class="handoffLayerSectionBody handoffLayerSectionContentBody"><div id="handoffArtboardFillsList"></div></div></div></div><div id="handoffArtboardAssets" style="display: none;"><div class="handoffLayerViewRow">
  <div class="handoffLayerViewCell">
    <span class="handoffCloseSection handoffCloseIcon">×</span>
  </div>
</div><div class="handoffLayerSection"><div class="handoffSectionName">Assets</div><div class="handoffLayerSectionBody handoffLayerSectionContentBody">
  <div id="handoffArtboardAssetsList"></div>
  <div id="handoffArtboardAssetsLayerList"></div>
<div></div></div></div></div><div id="handoffLayerView" style="display: none"><div id="handoffLayerNameSection">
  <div id="handoffLayerName"></div>
</div><div id="handoffStateSelectorSection" class="handoffLayerSection">
</div><div id="handoffDimensionsSection" class="handoffLayerSection">
  <div class="handoffSectionName">Size and position</div>
  <div class="handoffLayerSectionBody">
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        X: <span id="handoffDimensionsX" class="handoffLayerValue"></span>
      </div>
      <div class="handoffLayerViewCell">
        Y: <span id="handoffDimensionsY" class="handoffLayerValue"></span>
      </div>
    </div>
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Width: <span id="handoffDimensionsWidth" class="handoffLayerValue"></span>
      </div>
      <div class="handoffLayerViewCell">
        Height: <span id="handoffDimensionsHeight" class="handoffLayerValue"></span>
      </div>
    </div>
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Rotation: <span id="handoffDimensionsRotation" class="handoffLayerValue"></span>
      </div>
      <div class="handoffLayerViewCell">
        Radius: <span id="handoffDimensionsRadius" class="handoffLayerValue"></span>
      </div>
    </div>
    <div id="handoffDimensionsPaddingRow" class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Padding: <span id="handoffDimensionsPadding" class="handoffLayerValue"></span>
      </div>
    </div>
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Opacity: <span id="handoffDimensionsOpacity" class="handoffLayerValue"></span>
      </div>
    </div>
  </div>
</div><div id="handoffLayerAssetsSection" class="handoffLayerSection">
  <div class="handoffSectionName">
    <span>Assets</span>
    <div class="download-all-assets-container">
      <button type="button" class="handoff-download-btn">Download all</button>
    </div>
  </div>

  <div class="handoffLayerSectionBody">

  </div>
</div><div id="handoffLayerContentSection" class="handoffLayerSection">
  <div class="handoffSectionName">
    <span>Content</span>
    <div class="copy-to-clipboard-container"><div class="handoff-copy-to-clipboard">
  <div class="copy-to-clipboard-toast" style="display: none;">Copied to clipboard</div>
  <button type="button" title="Copy to clipboard" class="copy-to-clipboard-btn">
  </button>
</div></div>
  </div>
  <div class="handoffLayerSectionBody handoffLayerSectionContentBody">
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        <div class="handoffTextContentContainer">
          <div id="handoffTextContent" class="handoffLayerValue"></div>
        </div>
      </div>
    </div>
  </div>
</div><div id="handoffLayerTypefaceSection" class="handoffLayerSection">
  <div class="handoffSectionName">Typography</div>
  <div class="handoffLayerSectionBody">
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Typeface: <span id="handoffFontName" class="handoffLayerValue"></span>
      </div>
    </div>
    <div id="handoffFontProperties">
      <div class="handoffLayerViewRow">
        <div class="handoffLayerViewCell">
          Size: <span id="handoffFontSize" class="handoffLayerValue"></span>
        </div>
        <div class="handoffLayerViewCell">
          Align: <span id="handoffFontAlignment" class="handoffLayerValue"></span>
        </div>
      </div>
      <div class="handoffLayerViewRow">
        <div id="handoffTextColorContainer" class="handoffLayerViewCell">
      </div>
    </div>
  </div>
</div></div><div id="handoffLayerFillsSection" class="handoffLayerSection">
  <div class="handoffSectionName">Fill color</div>
  <div class="handoffLayerSectionBody">
    <div class="handoffLayerViewRow">
      <div id="handoffFillsListContainer" class="handoffLayerViewCell">
      </div>
    </div>
  </div>
</div><div id="handoffLayerBordersSection" class="handoffLayerSection">
  <div class="handoffSectionName">Border</div>
  <div class="handoffLayerSectionBody">
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Width: <span id="handoffBorderWidth" class="handoffLayerValue"></span>
      </div>
      <div class="handoffLayerViewCell">
        Position: <span id="handoffBorderAlignment" class="handoffLayerValue"></span>
      </div>
    </div>
    <div class="handoffLayerViewRow">
      <div id="handoffFillsListContainer" class="handoffLayerViewCell">
      </div>
    </div>
  </div>
</div><div id="handoffLayerShadowsSection" class="handoffLayerSection">
  <div class="handoffSectionName">Shadows</div>
  <div class="handoffLayerSectionBody">
      <div id="handoffShadowsListContainer" class="handoffLayerViewCell">
      </div>
    </div>
</div><div id="handoffStyleEffects">
</div><div id="handoffLayerHtmlSection" class="handoffLayerSection">
  <div class="handoffSectionName">HTML</div>
  <div class="handoffLayerSectionBody handoffLayerSectionContentBody">
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        <span id="handoffHtmlContent" class="handoffLayerValue"></span>
      </div>
    </div>
  </div>
</div><div id="cssContainer" class="handoffLayerSection">
  <div class="handoffSectionName">
    <span>CSS</span>
    <div class="copy-to-clipboard-container"><div class="handoff-copy-to-clipboard">
  <div class="copy-to-clipboard-toast" style="display: none;">Copied to clipboard</div>
  <button type="button" title="Copy to clipboard" class="copy-to-clipboard-btn">
  </button>
</div></div>
  </div>
  <div class="handoffLayerSectionBody">
    <div class="handoffLayerViewRow">
      <div id="cssDiv" class="handoffLayerViewCell">
      <div class="cm-editor ͼ1 ͼ3 ͼ4 ͼo ͼ15" style="height: auto;"><div class="cm-announced" aria-live="polite"></div><div tabindex="-1" class="cm-scroller"><div class="cm-gutters cm-gutters-before" aria-hidden="true" style="min-height: 14px; position: sticky;"><div class="cm-gutter cm-lineNumbers"><div class="cm-gutterElement" style="height: 0px; visibility: hidden; pointer-events: none;">9</div><div class="cm-gutterElement" style="height: 14px;">1</div></div></div><div spellcheck="false" autocorrect="off" autocapitalize="off" writingsuggestions="false" translate="no" contenteditable="true" class="cm-content cm-lineWrapping" role="textbox" aria-multiline="true" aria-readonly="true" data-language="css" style="tab-size: 4;"><div class="cm-line"><br></div></div><div class="cm-layer cm-layer-above cm-cursorLayer" aria-hidden="true" style="z-index: 150; animation-duration: 1200ms;"></div><div class="cm-layer cm-selectionLayer" aria-hidden="true" style="z-index: -2;"></div></div></div></div>
    </div>
  </div>
</div></div></div>
</div></div><div id="feedbackHost" class="rightPanel" style="display: none;"><div class="closeButtonContainerFb"><button></button></div><div class="relative flex min-h-0 flex-1 flex-col bg-white text-sm"><div class="flex flex-col gap-4 p-3"><div class="flex flex-row flex-nowrap items-center justify-between"><div class="font-semibold"> Comments </div><button type="button" class="inline-flex items-center justify-center gap-x-2 whitespace-nowrap rounded-sm leading-6 tracking-btn outline-offset-2 focus-visible:outline-1 data-disabled:pointer-events-none data-disabled:opacity-50 font-normal dark:text-slate-100 data-active:text-sky-400 dark:data-active:text-blue-300 data-[state=open]:bg-gray-200/40 dark:data-[state=open]:bg-gray-700/40 hover:bg-gray-200/40 dark:hover:bg-gray-700/40 focus-visible:outline-indigo-600 dark:focus-visible:outline-slate-100 size-7 flex-none text-slate-600 hover:text-slate-800 focus-visible:text-slate-800 dark:hover:text-slate-100 dark:focus-visible:text-slate-100 rounded-full" data-state="closed" data-grace-area-trigger=""><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x size-4" aria-hidden="true"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><span class="sr-only">Close</span></button></div><div class="flex flex-col gap-2"><button type="button" class="inline-flex items-center justify-center gap-x-2 whitespace-nowrap rounded-sm leading-6 tracking-btn outline-offset-2 focus-visible:outline-1 data-disabled:pointer-events-none data-disabled:opacity-50 font-medium text-white bg-blue-600 data-[state=open]:bg-blue-800 dark:bg-blue-400 dark:data-[state=open]:bg-blue-500 hover:bg-blue-800 dark:hover:bg-blue-500 focus-visible:outline-indigo-600 dark:focus-visible:outline-slate-100 data-active:text-blue-600 dark:data-active:text-blue-400 data-active:bg-sky-100 dark:data-active:bg-sky-200 data-active:hover:bg-sky-200 dark:data-active:hover:bg-sky-100 h-7 px-2">Add comment</button><!----></div><div class="flex flex-row items-center gap-2"><div class="relative flex w-full items-center"><div class="absolute inset-y-0 start-0 flex items-center p-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search-icon lucide-search size-4 text-slate-600 opacity-50" aria-hidden="true"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg></div><input type="text" placeholder="Search" class="block w-full rounded-sm border-0 outline-hidden disabled:cursor-not-allowed disabled:opacity-50 bg-white ring-1 ring-inset ring-slate-300 placeholder:text-slate-400 focus:bg-white hover:enabled:ring-slate-400 focus:enabled:ring-indigo-600 px-3 py-1.5 whitespace-nowrap px-8"><div class="absolute inset-y-0 end-0 flex items-center p-2"><button type="button" class="inline-flex items-center justify-center gap-x-2 whitespace-nowrap rounded-sm leading-6 tracking-btn outline-offset-2 focus-visible:outline-1 data-disabled:pointer-events-none data-disabled:opacity-50 font-normal dark:text-slate-100 data-active:text-sky-400 dark:data-active:text-blue-300 data-[state=open]:bg-gray-200/40 dark:data-[state=open]:bg-gray-700/40 hover:bg-gray-200/40 dark:hover:bg-gray-700/40 focus-visible:outline-indigo-600 dark:focus-visible:outline-slate-100 size-4 flex-none text-slate-600 hover:text-slate-800 focus-visible:text-slate-800 dark:hover:text-slate-100 dark:focus-visible:text-slate-100" aria-label="Clear" tabindex="-1" style="display: none;"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x size-4 text-slate-600" aria-hidden="true"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg></button></div></div><button type="button" class="inline-flex items-center justify-center gap-x-2 whitespace-nowrap rounded-sm leading-6 tracking-btn outline-offset-2 focus-visible:outline-1 data-disabled:pointer-events-none data-disabled:opacity-50 font-normal dark:text-slate-100 data-active:text-sky-400 dark:data-active:text-blue-300 data-[state=open]:bg-gray-200/40 dark:data-[state=open]:bg-gray-700/40 hover:bg-gray-200/40 dark:hover:bg-gray-700/40 focus-visible:outline-indigo-600 dark:focus-visible:outline-slate-100 size-7 flex-none text-slate-600 hover:text-slate-800 focus-visible:text-slate-800 dark:hover:text-slate-100 dark:focus-visible:text-slate-100" id="reka-dropdown-menu-trigger-v-0" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-list-filter-icon lucide-list-filter size-4"><path d="M3 6h18"></path><path d="M7 12h10"></path><path d="M10 18h4"></path></svg></button><button type="button" class="inline-flex items-center justify-center gap-x-2 whitespace-nowrap rounded-sm leading-6 tracking-btn outline-offset-2 focus-visible:outline-1 data-disabled:pointer-events-none data-disabled:opacity-50 font-normal dark:text-slate-100 data-active:text-sky-400 dark:data-active:text-blue-300 data-[state=open]:bg-gray-200/40 dark:data-[state=open]:bg-gray-700/40 hover:bg-gray-200/40 dark:hover:bg-gray-700/40 focus-visible:outline-indigo-600 dark:focus-visible:outline-slate-100 size-7 flex-none text-slate-600 hover:text-slate-800 focus-visible:text-slate-800 dark:hover:text-slate-100 dark:focus-visible:text-slate-100" id="reka-dropdown-menu-trigger-v-1" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis-icon lucide-ellipsis size-4"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg></button></div><div class="flex flex-row gap-1 flex-wrap"><!----><!----><!----></div></div><div class="flex flex-col items-stretch p-4 text-slate-500"><div class="border-t border-slate-300 pb-2 pt-4 text-center font-semibold"> No comments </div><div class="text-center"> Add a comment to give feedback, ask a question, or request a change. </div></div></div></div><div id="lsplitbar" class="splitbar" style="z-index: auto; display: none;"></div><div id="rsplitbar" class="splitbar" style="z-index: auto; display: none;"></div></div>

    <div id="maximizePanelContainer" class="maximizePanelContainer" style="display: block;">
        <div id="maximizePanelOver">
            <div id="maximizePanel" title="Expand" class="" style="left: -28px;">
                <div id="maximizeButton" class="maximizeButton maximizeIcon rotated"></div>
            </div>
        </div>
    </div>

    <div id="mobileControlFrameContainer"></div>

<!-- 11.0.0.4131 -->
<script src="./日历-ok_files/jquery-3.7.1.min.js"></script>
<script src="./日历-ok_files/jquery.nicescroll.min.js"></script>
<script src="./日历-ok_files/axutils.js"></script>
<script src="./日历-ok_files/messagecenter.js"></script>
<script src="./日历-ok_files/axplayer.js"></script>
<script src="./日历-ok_files/init.js"></script>


<script type="text/javascript">
                    (function() {
                        if ($axure && $axure.loadDocument) {
                            window.parent.postMessage("isAxurePrototype", "*");
                            var origLoadDocument = $axure.loadDocument;
                            $axure.loadDocument = function(doc) {
                                doc.configuration.prototypeId = 'DKHTG1';
                                doc.configuration.projectName = 'Halal-app';
                                origLoadDocument(doc);
                            };
                        }
                    })();
                </script><script src="./日历-ok_files/Expo.B6dLLT38.js"></script><link href="./日历-ok_files/style-BU0kO94W.css" rel="stylesheet"><script src="./日历-ok_files/AxLib.awkaAZnn.js"></script><link href="./日历-ok_files/style-BZVZlUFk.css" rel="stylesheet">

<!----><!----><!----><!----></body></html>