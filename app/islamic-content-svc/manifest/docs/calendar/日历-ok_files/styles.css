.ax_default {
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  line-height:normal;
  text-transform:none;
}
.box_1 {
}
.primary_button {
  color:#FFFFFF;
}
.box_2 {
}
.box_3 {
}
.ellipse {
}
.image {
}
.primary_button1 {
  color:#FFFFFF;
}
.link_button {
  color:#1E98D7;
}
.heading_1 {
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.heading_2 {
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
.heading_3 {
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
.shape {
}
.heading_4 {
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
.heading_5 {
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
.heading_6 {
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
.label {
  font-size:13px;
  text-align:left;
}
.paragraph {
  text-align:left;
}
.line {
}
.text_area {
  color:#000000;
  text-align:left;
}
.table_cell {
}
.connector {
}
.sticky_4 {
  text-align:left;
}
.form_hint {
  color:#999999;
}
.form_disabled {
}
.flow_shape {
}
.icon {
}
.button {
}
.line1 {
}
.link_button1 {
  color:#169BD5;
}
._图片 {
}
._36号字_顶部标题、大按钮、弹窗提示主标题_ {
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
._32号字_单行列表内，右方操作说明的文字__） {
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
._28号字_页面备注信息及列表的表头说明文字__） {
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
._24号字_最小说明文本，例如列表时间态、版权信息等） {
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
.image1 {
  color:#000000;
}
textarea, select, input, button, div, svg { outline: none; }
