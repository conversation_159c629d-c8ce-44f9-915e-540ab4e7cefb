<!DOCTYPE html>
<!-- saved from url=(0053)https://dkhtg1.axshare.com/%E6%97%A5%E5%8E%86-ok.html -->
<html zoom="89" class="hideScroll" style=""><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="robots" content="noindex, nofollow">
   <link href="./css2" rel="stylesheet">
    <title>日历-ok</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <link href="./axure_rp_page.css" type="text/css" rel="stylesheet">
    <link href="./styles.css" type="text/css" rel="stylesheet">
    <link href="./styles(1).css" type="text/css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/" rel="preconnect">
    <link href="https://fonts.gstatic.com/" rel="preconnect">
    <link href="./css2(1)" rel="stylesheet">
      <link rel="icon" href="https://app.axure.cloud/app/favicon.ico"><meta property="og:site_name" content="Axure Cloud">
<meta property="og:type" content="website">
<meta property="og:title" content="Halal-app - 日历-ok">
<meta property="og:description" content="Created with Axure RP">
<meta property="og:url" content="https://dkhtg1.axshare.com/%E6%97%A5%E5%8E%86-ok.html">
<meta property="og:image" content="https://files.axshare.com/gsc/DKHTG1/thumbnails/uboha9.png">
<link rel="alternate" type="application/json+oembed" href="https://app.axure.cloud/oembed?url=https%3A%2F%2Fdkhtg1.axshare.com%2F%E6%97%A5%E5%8E%86-ok.html" title="oEmbed"><script src="./axPhishingBanner.js"></script>
  <link href="./axPhishingBanner.css" type="text/css" rel="stylesheet"><script type="text/javascript">
        AXSHARE_HOST_URL = 'http://app.axure.cloud';
        AXSHARE_HOST_SECURE_URL = 'https://app.axure.cloud';
        ACCOUNT_SERVICE_URL = 'http://accounts.axure.com';
        ACCOUNT_SERVICE_SECURE_URL = 'https://accounts.axure.com';
        ON_PREM_LDAP_ENABLED = 'false';
        AXSHARE_CLIENT_URL = 'https://app.axure.cloud/app'
</script><script src="./jquery-3.7.1.min.js"></script>
    <script src="./axQuery.js"></script>
    <script src="./globals.js"></script>
    <script src="./axutils.js"></script>
    <script src="./annotation.js"></script>
    <script src="./axQuery.std.js"></script>
    <script src="./doc.js"></script>
    <script src="./messagecenter.js"></script>
    <script src="./events.js"></script>
    <script src="./recording.js"></script>
    <script src="./action.js"></script>
    <script src="./expr.js"></script>
    <script src="./geometry.js"></script>
    <script src="./flyout.js"></script>
    <script src="./model.js"></script>
    <script src="./repeater.js"></script>
    <script src="./sto.js"></script>
    <script src="./utils.temp.js"></script>
    <script src="./variables.js"></script>
    <script src="./drag.js"></script>
    <script src="./move.js"></script>
    <script src="./visibility.js"></script>
    <script src="./style.js"></script>
    <script src="./adaptive.js"></script>
    <script src="./tree.js"></script>
    <script src="./init.temp.js"></script>
    <script src="./legacy.js"></script>
    <script src="./viewer.js"></script>
    <script src="./math.js"></script>
    <script src="./jquery.nicescroll.min.js"></script>
    <script src="./document.js"></script>
    <script src="./data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'https://files.axshare.com/gsr/4131/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'https://files.axshare.com/gsr/4131/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  
<script>
    $(document).ready(function(){
        $("iframe").each(function( index ) {
            var iframeSrc = $(this).attr('src') || '';
            if(iframeSrc.indexOf('http:') != -1){
                $(this).attr('scrolling', 'auto');
                $(this).css('overflow', 'auto');
                $(this).attr('src', 'https://app.axure.cloud/prototype/showFrameUrlChangePage');
            }
        });
    });
</script></head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Image) -->
      <div id="u0" class="ax_default image transition notrs">
        <img id="u0_img" class="img " src="./u0.png">
        <div id="u0_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u1" class="ax_default box_1 transition notrs">
        <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u1.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u1_img" class="img generatedImage">

  <defs>
    <pattern id="u1_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u1_img_cl40">
      <path d="M 0 75  L 0 0  L 62 0  L 62 75  L 0 75  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -277 -408 )">
    <path d="M 0 75  L 0 0  L 62 0  L 62 75  L 0 75  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0)" stroke="none" transform="matrix(1 0 0 1 277 408 )" class="fill"></path>
    <path d="M 0 75  L 0 0  L 62 0  L 62 75  L 0 75  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(217, 0, 27, 1)" fill="none" transform="matrix(1 0 0 1 277 408 )" class="stroke" mask="url(#u1_img_cl40)"></path>
  </g>
        </svg>
        <div id="u1_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u2" class="ax_default image transition notrs">
        <img id="u2_img" class="img " src="./u2.png">
        <div id="u2_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u3" class="ax_default image transition notrs">
        <img id="u3_img" class="img " src="./u3.png">
        <div id="u3_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u4" class="ax_default image transition notrs">
        <img id="u4_img" class="img " src="./u4.png">
        <div id="u4_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u5" class="ax_default image transition notrs">
        <img id="u5_img" class="img " src="./u5.png">
        <div id="u5_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u6" class="ax_default image transition notrs">
        <img id="u6_img" class="img " src="./u6.png">
        <div id="u6_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u7" class="ax_default image transition notrs">
        <img id="u7_img" class="img " src="./u7.png">
        <div id="u7_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u8" class="ax_default box_1 transition notrs">
        <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u8.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u8_img" class="img generatedImage">

  <defs>
    <pattern id="u8_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u8_img_cl41">
      <path d="M 0 43  L 0 0  L 37 0  L 37 43  L 0 43  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -928 -136 )">
    <path d="M 0 43  L 0 0  L 37 0  L 37 43  L 0 43  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0)" stroke="none" transform="matrix(1 0 0 1 928 136 )" class="fill"></path>
    <path d="M 0 43  L 0 0  L 37 0  L 37 43  L 0 43  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(217, 0, 27, 1)" fill="none" transform="matrix(1 0 0 1 928 136 )" class="stroke" mask="url(#u8_img_cl41)"></path>
  </g>
        </svg>
        <div id="u8_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u9" class="ax_default box_1 transition notrs">
        <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u9.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u9_img" class="img generatedImage">

  <defs>
    <pattern id="u9_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u9_img_cl42">
      <path d="M 0 28  L 0 0  L 243 0  L 243 28  L 0 28  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -758 -446 )">
    <path d="M 0 28  L 0 0  L 243 0  L 243 28  L 0 28  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0)" stroke="none" transform="matrix(1 0 0 1 758 446 )" class="fill"></path>
    <path d="M 0 28  L 0 0  L 243 0  L 243 28  L 0 28  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(217, 0, 27, 1)" fill="none" transform="matrix(1 0 0 1 758 446 )" class="stroke" mask="url(#u9_img_cl42)"></path>
  </g>
        </svg>
        <div id="u9_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u10" class="ax_default paragraph transition notrs">
        <div id="u10_div" class=""></div>
        <div id="u10_text" class="text ">
          <p><span>概述：</span></p><p><span>本模块提供基于公历、Hijriah（伊斯兰历）和爪哇历（Javanese）三重日历展示模式，整合国家节日、伊斯兰重要纪念日、宗教活动（如斋戒日）、以及自定义提醒，支持个性化Hijriah历法算法与日期校准选项，便于穆斯林用户进行宗教活动规划。</span></p>
        </div>
      </div>

      <!-- Unnamed (功能引导) -->
      <div id="u11" class="nopointer  ax_default">

        <!-- Unnamed (Line) -->
        <div id="u12" class="ax_default line transition notrs">
          <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u12.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u12_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 16 -10 )">
    <path d="M 0 3  L 32 3  " stroke-width="6" stroke-dasharray="0" stroke="rgba(32, 97, 243, 1)" fill="none" transform="matrix(1 0 0 1 -13 13 )" class="stroke"></path>
  </g>
          </svg>
          <div id="u12_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u13" class="ax_default box_2 transition notrs">
          <div id="u13_div" class=""></div>
          <div id="u13_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u14" class="ax_default heading_3 transition notrs">
        <div id="u14_div" class=""></div>
        <div id="u14_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">首页</span><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">-</span><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">快捷入口-日历</span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u15" class="ax_default">

        <!-- Unnamed (Table cell) -->
        <div id="u16" class="ax_default table_cell transition notrs">
          <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u16.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u16_img" class="img generatedImage" viewBox="0 0 98 30">

  <path d="M 1 1  L 98 1  L 98 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(202, 249, 130, 1)" stroke="none" class="fill"></path>
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" class="stroke"></path>
  <path d="M 0 0.5  L 98 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" class="stroke"></path>
          </svg>
          <div id="u16_text" class="text ">
            <p><span>功能项</span></p>
          </div>
        </div>

        <!-- Unnamed (Table cell) -->
        <div id="u17" class="ax_default table_cell transition notrs">
          <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u17.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u17_img" class="img generatedImage" viewBox="98 0 184 30">

  <path d="M 1 1  L 183 1  L 183 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(202, 249, 130, 1)" stroke="none" transform="matrix(1 0 0 1 98 0 )" class="fill"></path>
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 98 0 )" class="stroke"></path>
  <path d="M 0 0.5  L 184 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 98 0 )" class="stroke"></path>
  <path d="M 183.5 1  L 183.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 98 0 )" class="stroke"></path>
          </svg>
          <div id="u17_text" class="text ">
            <p><span>说明</span></p>
          </div>
        </div>

        <!-- Unnamed (Table cell) -->
        <div id="u18" class="ax_default table_cell transition notrs">
          <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u18.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u18_img" class="img generatedImage" viewBox="0 30 98 30">

  <path d="M 1 1  L 98 1  L 98 29  L 1 29  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 30 )" class="fill"></path>
  <path d="M 0.5 1  L 0.5 29  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 30 )" class="stroke"></path>
  <path d="M 0 0.5  L 98 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 30 )" class="stroke"></path>
  <path d="M 0 29.5  L 98 29.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 30 )" class="stroke"></path>
          </svg>
          <div id="u18_text" class="text ">
            <p><span>日历</span></p>
          </div>
        </div>

        <!-- Unnamed (Table cell) -->
        <div id="u19" class="ax_default table_cell transition notrs">
          <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u19.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u19_img" class="img generatedImage" viewBox="98 30 184 30">

  <path d="M 1 1  L 183 1  L 183 29  L 1 29  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 98 30 )" class="fill"></path>
  <path d="M 0.5 1  L 0.5 29  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 98 30 )" class="stroke"></path>
  <path d="M 0 0.5  L 184 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 98 30 )" class="stroke"></path>
  <path d="M 183.5 1  L 183.5 29  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 98 30 )" class="stroke"></path>
  <path d="M 0 29.5  L 184 29.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 98 30 )" class="stroke"></path>
          </svg>
          <div id="u19_text" class="text ">
            <p><span>快捷入口</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u20" class="ax_default heading_3 transition notrs">
        <div id="u20_div" class=""></div>
        <div id="u20_text" class="text ">
          <p><span>功能说明：</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u21" class="ax_default paragraph transition notrs">
        <div id="u21_div" class=""></div>
        <div id="u21_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日历视图展示</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp; 1</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）显示当前月份的公历日期、</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日期、爪哇五日历（</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">Wage, Pon</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">等）。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp; 2</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）重点事件以颜色点标注，如国家节假日、宗教纪念日、斋戒日等。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp; 3</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）点击具体日期可查看当天事件</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">/</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">节日详情。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp; 4</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）支持左右滑动或点击箭头切换月份。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp; 5</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）月份顶部展示当前公历</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">+Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">月份，如</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">“Juli 2025 / Muharram–Safar 1447”</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">。</span></p>
        </div>
      </div>

      <!-- Unnamed (功能引导) -->
      <div id="u22" class="nopointer  ax_default">

        <!-- Unnamed (Line) -->
        <div id="u23" class="ax_default line transition notrs">
          <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u12.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u23_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 16 -10 )">
    <path d="M 0 3  L 32 3  " stroke-width="6" stroke-dasharray="0" stroke="rgba(32, 97, 243, 1)" fill="none" transform="matrix(1 0 0 1 -13 13 )" class="stroke"></path>
  </g>
          </svg>
          <div id="u23_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u24" class="ax_default box_2 transition notrs">
          <div id="u24_div" class=""></div>
          <div id="u24_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u25" class="ax_default heading_3 transition notrs">
        <div id="u25_div" class=""></div>
        <div id="u25_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">首页</span><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">-</span><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">快捷入口-日历主页</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u26" class="ax_default box_1 transition notrs">
        <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u9.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u26_img" class="img generatedImage">

  <defs>
    <pattern id="u26_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u26_img_cl42">
      <path d="M 0 28  L 0 0  L 243 0  L 243 28  L 0 28  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -758 -446 )">
    <path d="M 0 28  L 0 0  L 243 0  L 243 28  L 0 28  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0)" stroke="none" transform="matrix(1 0 0 1 758 446 )" class="fill"></path>
    <path d="M 0 28  L 0 0  L 243 0  L 243 28  L 0 28  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(217, 0, 27, 1)" fill="none" transform="matrix(1 0 0 1 758 446 )" class="stroke" mask="url(#u26_img_cl42)"></path>
  </g>
        </svg>
        <div id="u26_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u27" class="ax_default paragraph transition notrs">
        <div id="u27_div" class=""></div>
        <div id="u27_text" class="text ">
          <p><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">Hari Besar &amp; Libur Nasional</span></p><p><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">国家节假日与纪念日信息</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u28" class="ax_default paragraph transition notrs">
        <div id="u28_div" class=""></div>
        <div id="u28_text" class="text ">
          <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp; 1</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）展示国家公共假期与宗教节日列表（支持下拉折叠）。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp; 2</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）每项节日标记日期、公历与</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日期、标题及简要描述。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp; 3</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）支持点击节日进入详细说明或资料。</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u29" class="ax_default paragraph transition notrs">
        <div id="u29_div" class=""></div>
        <div id="u29_text" class="text ">
          <p><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;">Puasa</span></p><p><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;font-style:normal;">斋戒日提醒</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u30" class="ax_default paragraph transition notrs">
        <div id="u30_div" class=""></div>
        <div id="u30_text" class="text ">
          <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;&nbsp; 1</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）展示本月推荐斋戒日，如</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">“Puasa Senin Kamis”</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">、</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">“Puasa Asyura”</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">等。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;&nbsp; 2</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）每项包含：名称、公历与</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日期、星期、说明图标。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;&nbsp; 3</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">）点击可查看说明或跳转到相关祷告页面。</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u31" class="ax_default paragraph transition notrs">
        <div id="u31_div" class=""></div>
        <div id="u31_text" class="text ">
          <p><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">Hijriah</span><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">历法说明弹窗</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">&nbsp; 1</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">）点击右上角提示按钮进入</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">历时提示弹窗：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">“</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">告知</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">历法为预测性工具，需以</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">“rukyat hilal</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">（新月目视）</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">”</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">为准。</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">”</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">&nbsp; 2</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">）弹窗按钮：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">“Mengerti</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">（我知道了）</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">”</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">，点击关闭提示。</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u32" class="ax_default paragraph transition notrs">
        <div id="u32_div" class=""></div>
        <div id="u32_text" class="text ">
          <p><span>Kalender Hijriah ini hanya membantu.</span></p><p><span><br></span></p><p><span>Penentuan awal bulan Hijriah menunggu hasil rukyat hilal.</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u33" class="ax_default paragraph transition notrs">
        <div id="u33_div" class=""></div>
        <div id="u33_text" class="text ">
          <p><span>Pengingat</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u34" class="ax_default paragraph transition notrs">
        <div id="u34_div" class=""></div>
        <div id="u34_text" class="text ">
          <p><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">Hijriah</span><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">历法设置（</span><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">Pengaturan Kalender Hijriah</span><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">）</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;"><br></span></p><p><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">&nbsp; 1</span><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">）方法选择（</span><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">Metode Perhitungan</span><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">）：</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">自动（默认）：系统根据用户所处时区与国家，优先匹配当地通行历法，如印尼默认采用</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;"> Lembaga Falakiyah NU</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">；无法识别则退回</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;"> Ummul Qura</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">。</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;"><br></span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u35" class="ax_default paragraph transition notrs">
        <div id="u35_div" class=""></div>
        <div id="u35_text" class="text ">
          <p><span>NU历法：需定期更新。</span></p><p><span><br></span></p><p><span>Ummul Qura（沙特标准）：采用沙特阿拉伯 Umm al-Qura 历法算法，通常来源于政府公开数据集或开源历法库（如 https://github.com/msarhan/ummalqura-calendar）</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u36" class="ax_default paragraph transition notrs">
        <div id="u36_div" class=""></div>
        <div id="u36_text" class="text ">
          <p><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">&nbsp; 2</span><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">）</span><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">Hijriah</span><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">日期校正（</span><span style="font-family:&quot;Arial-BoldMT&quot;, &quot;Arial Bold&quot;, &quot;Arial&quot;, sans-serif;font-weight:700;">Penyesuaian Tanggal</span><span style="font-family:&quot;PingFangSC-Semibold&quot;, &quot;PingFang SC Semibold&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:650;">）：</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">&nbsp;&nbsp;&nbsp; </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">为应对不同地区因</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">“</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">目测新月（</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">rukyat hilal</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">）</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">”</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">实际观察日不同而导致的偏差，允许用户手动调整</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;font-weight:400;">日期。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;font-weight:400;"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u37" class="ax_default paragraph transition notrs">
        <div id="u37_div" class=""></div>
        <div id="u37_text" class="text ">
          <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;&nbsp; </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">默认设定为</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">“</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">自动</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">”</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">。每项点击可进入对应选项页进行手动偏移调整，进行</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">-3</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">天至</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">+3</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">天，适应不同用户所在地区</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">rukyat</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">误差。</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u38" class="ax_default paragraph transition notrs">
        <div id="u38_div" class=""></div>
        <div id="u38_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">示例：若系统判定今天为</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> 13 Muharram</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">，用户选择「</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">+2 </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日」，则页面显示日期为</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> 15 Muharram</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">-3 </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">10 Muharram</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">，当前</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日期</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> - 3</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">-2 </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">11 Muharram</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">，当前</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日期</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> - 2</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">-1 </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">12 Muharram</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">，当前</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日期</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> - 1</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">自动（默认）：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">13 Muharram</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">不做偏移。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">+1 </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">：日</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">14 Muharram</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">当前</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日期</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> + 1</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">+2 </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">：日</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">15 Muharram</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">当前</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日期</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> + 2</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">。</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">+3 </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">：日</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">16 Muharram</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">当前</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">Hijriah</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">日期</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> + 3</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">。</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u39" class="ax_default paragraph transition notrs">
        <div id="u39_div" class=""></div>
        <div id="u39_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">系统推荐逻辑：</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">首次安装时默认开启自动模式，根据地理位置优先使用：</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">○</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">印尼</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> → Lembaga Falakiyah NU</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">○</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">中东</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> → Ummul Qura</span></p><p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">○</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">其他地区</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"> → Ummul Qura </span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">或国际开源历法</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;PingFang SC&quot;, sans-serif;">若用户手动更改历法或校正值，系统保存设置并在每次加载时优先使用自定义配置。</span></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u40" class="ax_default connector">
        <img id="u40_seg0" class="img " src="./u40_seg0.svg" alt="u40_seg0">
        <img id="u40_seg1" class="img " src="./u40_seg1.svg" alt="u40_seg1">
        <img id="u40_seg2" class="img " src="./u40_seg2.svg" alt="u40_seg2">
        <img id="u40_seg3" class="img " src="./u40_seg3.svg" alt="u40_seg3">
        <div id="u40_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u41" class="ax_default box_1 transition notrs">
        <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u8.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u41_img" class="img generatedImage">

  <defs>
    <pattern id="u41_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u41_img_cl41">
      <path d="M 0 43  L 0 0  L 37 0  L 37 43  L 0 43  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -928 -136 )">
    <path d="M 0 43  L 0 0  L 37 0  L 37 43  L 0 43  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0)" stroke="none" transform="matrix(1 0 0 1 928 136 )" class="fill"></path>
    <path d="M 0 43  L 0 0  L 37 0  L 37 43  L 0 43  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(217, 0, 27, 1)" fill="none" transform="matrix(1 0 0 1 928 136 )" class="stroke" mask="url(#u41_img_cl41)"></path>
  </g>
        </svg>
        <div id="u41_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u42" class="ax_default connector">
        <img id="u42_seg0" class="img " src="./u42_seg0.svg" alt="u42_seg0">
        <img id="u42_seg1" class="img " src="./u42_seg1.svg" alt="u42_seg1">
        <img id="u42_seg2" class="img " src="./u42_seg2.svg" alt="u42_seg2">
        <img id="u42_seg3" class="img " src="./u42_seg3.svg" alt="u42_seg3">
        <div id="u42_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u43" class="ax_default box_1 transition notrs">
        <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u43.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u43_img" class="img generatedImage">

  <defs>
    <pattern id="u43_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u43_img_cl43">
      <path d="M 0 43  L 0 0  L 239 0  L 239 43  L 0 43  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -1987 -213 )">
    <path d="M 0 43  L 0 0  L 239 0  L 239 43  L 0 43  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0)" stroke="none" transform="matrix(1 0 0 1 1987 213 )" class="fill"></path>
    <path d="M 0 43  L 0 0  L 239 0  L 239 43  L 0 43  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(217, 0, 27, 1)" fill="none" transform="matrix(1 0 0 1 1987 213 )" class="stroke" mask="url(#u43_img_cl43)"></path>
  </g>
        </svg>
        <div id="u43_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u44" class="ax_default box_1 transition notrs">
        <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u44.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u44_img" class="img generatedImage">

  <defs>
    <pattern id="u44_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u44_img_cl44">
      <path d="M 0 28  L 0 0  L 239 0  L 239 28  L 0 28  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -1987 -262 )">
    <path d="M 0 28  L 0 0  L 239 0  L 239 28  L 0 28  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0)" stroke="none" transform="matrix(1 0 0 1 1987 262 )" class="fill"></path>
    <path d="M 0 28  L 0 0  L 239 0  L 239 28  L 0 28  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(217, 0, 27, 1)" fill="none" transform="matrix(1 0 0 1 1987 262 )" class="stroke" mask="url(#u44_img_cl44)"></path>
  </g>
        </svg>
        <div id="u44_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u45" class="ax_default connector">
        <img id="u45_seg0" class="img " src="./u45_seg0.svg" alt="u45_seg0">
        <img id="u45_seg1" class="img " src="./u45_seg1.svg" alt="u45_seg1">
        <img id="u45_seg2" class="img " src="./u45_seg2.svg" alt="u45_seg2">
        <img id="u45_seg3" class="img " src="./u45_seg3.svg" alt="u45_seg3">
        <img id="u45_seg4" class="img " src="./u45_seg4.svg" alt="u45_seg4">
        <div id="u45_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u46" class="ax_default connector">
        <img id="u46_seg0" class="img " src="./u46_seg0.svg" alt="u46_seg0">
        <img id="u46_seg1" class="img " src="./u46_seg1.svg" alt="u46_seg1">
        <img id="u46_seg2" class="img " src="./u46_seg2.svg" alt="u46_seg2">
        <img id="u46_seg3" class="img " src="./u46_seg3.svg" alt="u46_seg3">
        <div id="u46_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (end) -->
      <div id="u47" class="nopointer  ax_default">

        <!-- Unnamed (Line) -->
        <div id="u48" class="ax_default line transition notrs">
          <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u48.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u48_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 0 -31 )">
    <path d="M 0 0.5  L 400 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 31 )" class="stroke"></path>
  </g>
          </svg>
          <div id="u48_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u49" class="ax_default heading_2 transition notrs">
          <div id="u49_div" class=""></div>
          <div id="u49_text" class="text ">
            <p><span>END</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u50" class="ax_default box_1 transition notrs">
        <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u50.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u50_img" class="img generatedImage">

  <defs>
    <pattern id="u50_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u50_img_cl45">
      <path d="M 0 49  L 0 0  L 243 0  L 243 49  L 0 49  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -758 -706 )">
    <path d="M 0 49  L 0 0  L 243 0  L 243 49  L 0 49  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0)" stroke="none" transform="matrix(1 0 0 1 758 706 )" class="fill"></path>
    <path d="M 0 49  L 0 0  L 243 0  L 243 49  L 0 49  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(194, 128, 255, 1)" fill="none" transform="matrix(1 0 0 1 758 706 )" class="stroke" mask="url(#u50_img_cl45)"></path>
  </g>
        </svg>
        <div id="u50_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u51" class="ax_default connector">
        <img id="u51_seg0" class="img " src="./u51_seg0.svg" alt="u51_seg0">
        <img id="u51_seg1" class="img " src="./u51_seg1.svg" alt="u51_seg1">
        <img id="u51_seg2" class="img " src="./u51_seg2.svg" alt="u51_seg2">
        <div id="u51_text" class="text ">
          <p><span>点击进入详情页</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u52" class="ax_default box_1 transition notrs">
        <svg data="https://files.axshare.com/gsc/DKHTG1/0f/68/ed/0f68edac287c46f3b8a26a0526cabcf7/images/日历-ok/u52.svg?pageId=157ccabb-20f9-4a98-8110-f6a8249e48ba" id="u52_img" class="img generatedImage">

  <defs>
    <pattern id="u52_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u52_img_cl49">
      <path d="M 0 49  L 0 0  L 41 0  L 41 49  L 0 49  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -1361 -851 )">
    <path d="M 0 49  L 0 0  L 41 0  L 41 49  L 0 49  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0)" stroke="none" transform="matrix(1 0 0 1 1361 851 )" class="fill"></path>
    <path d="M 0 49  L 0 0  L 41 0  L 41 49  L 0 49  Z " stroke-width="2" stroke-dasharray="9,4" stroke="rgba(194, 128, 255, 1)" fill="none" transform="matrix(1 0 0 1 1361 851 )" class="stroke" mask="url(#u52_img_cl49)"></path>
  </g>
        </svg>
        <div id="u52_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Connector) -->
      <div id="u53" class="ax_default connector">
        <img id="u53_seg0" class="img " src="./u53_seg0.svg" alt="u53_seg0">
        <img id="u53_seg1" class="img " src="./u53_seg1.svg" alt="u53_seg1">
        <img id="u53_seg2" class="img " src="./u53_seg2.svg" alt="u53_seg2">
        <img id="u53_seg3" class="img " src="./u53_seg3.svg" alt="u53_seg3">
        <img id="u53_seg4" class="img " src="./u53_seg4.svg" alt="u53_seg4">
        <div id="u53_text" class="text ">
          <p><span>返回上一页</span></p>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u54" class="ax_default paragraph transition notrs">
        <div id="u54_div" class=""></div>
        <div id="u54_text" class="text ">
          <p><span>Mengerti</span></p>
        </div>
      </div>
    </div>
    <script src="./ios.js"></script>
  <script type="text/javascript">
                    (function() {
                        if ($axure && $axure.loadDocument) {
                            window.parent.postMessage("isAxurePrototype", "*");
                            var origLoadDocument = $axure.loadDocument;
                            $axure.loadDocument = function(doc) {
                                doc.configuration.prototypeId = 'DKHTG1';
                                doc.configuration.projectName = 'Halal-app';
                                origLoadDocument(doc);
                            };
                        }
                    })();
                </script>

</body></html>