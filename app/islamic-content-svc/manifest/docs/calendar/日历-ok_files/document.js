$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,d,p,d,q,r,s,d),t,_(u,[_(v,w,x,y,z,A,B,w,C,[_(v,D,x,E,z,F,B,G)]),_(v,w,x,H,z,A,B,w,C,[_(v,I,x,J,z,F,B,K)]),_(v,w,x,L,z,A,B,w,C,[_(v,w,x,M,z,A,B,w,C,[_(v,N,x,O,z,F,B,P),_(v,Q,x,R,z,F,B,S),_(v,T,x,U,z,F,B,V),_(v,W,x,X,z,F,B,Y),_(v,Z,x,ba,z,F,B,bb),_(v,w,x,bc,z,A,B,w,C,[_(v,bd,x,be,z,F,B,bf),_(v,bg,x,bh,z,F,B,bi),_(v,bj,x,bk,z,F,B,bl),_(v,bm,x,bn,z,F,B,bo),_(v,bp,x,bq,z,F,B,br),_(v,bs,x,bt,z,F,B,bu),_(v,bv,x,bw,z,F,B,bx,C,[_(v,w,x,by,z,A,B,w,C,[_(v,bz,x,bA,z,F,B,bB),_(v,bC,x,bD,z,F,B,bE)]),_(v,bF,x,bG,z,F,B,bH),_(v,bI,x,bJ,z,F,B,bK),_(v,bL,x,bM,z,F,B,bN)])]),_(v,bO,x,bP,z,F,B,bQ),_(v,bR,x,bS,z,F,B,bT),_(v,bU,x,bV,z,F,B,bW),_(v,bX,x,bY,z,F,B,bZ),_(v,ca,x,cb,z,F,B,cc),_(v,cd,x,ce,z,F,B,cf)]),_(v,w,x,cg,z,A,B,w,C,[_(v,ch,x,ci,z,F,B,cj),_(v,ck,x,bh,z,F,B,cl),_(v,cm,x,cn,z,F,B,co),_(v,cp,x,bq,z,F,B,cq),_(v,cr,x,cs,z,F,B,ct),_(v,cu,x,cv,z,F,B,cw),_(v,cx,x,bM,z,F,B,cy)]),_(v,w,x,cz,z,A,B,w),_(v,w,x,cA,z,A,B,w,C,[_(v,cB,x,ci,z,F,B,cC),_(v,cD,x,cE,z,F,B,cF,C,[_(v,cG,x,cH,z,F,B,cI),_(v,cJ,x,cK,z,F,B,cL),_(v,cM,x,cN,z,F,B,cO),_(v,cP,x,cQ,z,F,B,cR),_(v,cS,x,cT,z,F,B,cU)]),_(v,cV,x,cW,z,F,B,cX,C,[_(v,cY,x,cZ,z,F,B,da),_(v,db,x,dc,z,F,B,dd),_(v,de,x,df,z,F,B,dg),_(v,dh,x,di,z,F,B,dj),_(v,dk,x,dl,z,F,B,dm),_(v,dn,x,dp,z,F,B,dq)]),_(v,dr,x,ds,z,F,B,dt),_(v,du,x,dv,z,F,B,dw),_(v,dx,x,dy,z,F,B,dz,C,[_(v,dA,x,dB,z,F,B,dC),_(v,dD,x,dE,z,F,B,dF),_(v,dG,x,dH,z,F,B,dI),_(v,dJ,x,dK,z,F,B,dL)]),_(v,dM,x,dN,z,F,B,dO),_(v,dP,x,dQ,z,F,B,dR)])]),_(v,w,x,dS,z,A,B,w,C,[_(v,dT,x,dU,z,F,B,dV),_(v,dW,x,dX,z,F,B,dY),_(v,dZ,x,ea,z,F,B,eb),_(v,ec,x,ed,z,F,B,ee),_(v,ef,x,eg,z,F,B,eh)])]),ei,[ej,ek,el,em],en,[eo,ep,eq],er,_(es,w),et,_(eu,_(v,ev,ew,ex,ey,ez,eA,eB,eC,eD,eE,_(eF,eG,eH,eI),eJ,eK,eL,f,eM,eN,eO,eB,eP,eB,eQ,eR,eS,f,eT,_(eU,eV,eW,eV),eX,_(eY,eV,eZ,eV),fa,fb,fc,d,fd,f,fe,ev,ff,_(eF,eG,eH,fg),fh,_(eF,eG,eH,fi),fj,fk,fl,eG,fm,[fn],fo,fk,fp,fq,fr,fs,ft,fs,fu,fv,fw,fx,fy,fx,fz,fx,fA,fx,fB,_(),fC,null,fD,null,fE,fq,fF,_(fG,f,fH,fI,fJ,fI,fK,fI,fL,eV,eH,_(fM,fn,fN,fn,fO,fn,fP,fQ)),fR,_(fG,f,fH,eV,fJ,fI,fK,fI,fL,eV,eH,_(fM,fn,fN,fn,fO,fn,fP,fQ)),fS,_(fG,f,fH,fT,fJ,fT,fK,fI,fL,eV,eH,_(fM,fn,fN,fn,fO,fn,fP,fU)),fV,_(fG,f,fW,fX),fY,_(fG,f,fW,fX),fZ,ga,gb,_(gc,fn,gd,eV,ge,eR),gf,_(gg,fT,gh,fT,gi,eV,gj,eV,gk,eV),gl,_(eU,gm,eW,gm)),gn,_(go,_(v,gp),gq,_(v,gr,eE,_(eF,eG,eH,fg),fj,fq,fp,eD,ff,_(eF,eG,eH,gs)),gt,_(v,gu,fj,fq,ff,_(eF,eG,eH,gv)),gw,_(v,gx,fj,fq,ff,_(eF,eG,eH,gy)),gz,_(v,gA),fC,_(v,gB,fj,fq),gC,_(v,gD,eE,_(eF,eG,eH,fg),fj,fq,fp,eD,ff,_(eF,eG,eH,gE)),gF,_(v,gG,eE,_(eF,eG,eH,gE),fj,fq,ff,_(eF,eG,eH,gH,fo,eV)),gI,_(v,gJ,eJ,gK,ey,gL,fj,fq,ff,_(eF,eG,eH,gH,fo,eV),eM,gM,fu,gN,fw,fq,fy,fq,fz,fq,fA,fq),gO,_(v,gP,eJ,gQ,ey,gL,fj,fq,ff,_(eF,eG,eH,gH,fo,eV),eM,gM,fu,gN,fw,fq,fy,fq,fz,fq,fA,fq),gR,_(v,gS,eJ,gT,ey,gL,fj,fq,ff,_(eF,eG,eH,gH,fo,eV),eM,gM,fu,gN,fw,fq,fy,fq,fz,fq,fA,fq),gU,_(v,gV),gW,_(v,gX,eJ,gY,ey,gL,fj,fq,ff,_(eF,eG,eH,gH,fo,eV),eM,gM,fu,gN,fw,fq,fy,fq,fz,fq,fA,fq),gZ,_(v,ha,ey,gL,fj,fq,ff,_(eF,eG,eH,gH,fo,eV),eM,gM,fu,gN,fw,fq,fy,fq,fz,fq,fA,fq),hb,_(v,hc,eJ,hd,ey,gL,fj,fq,ff,_(eF,eG,eH,gH,fo,eV),eM,gM,fu,gN,fw,fq,fy,fq,fz,fq,fA,fq),he,_(v,hf,eJ,eK,fj,fq,ff,_(eF,eG,eH,gH,fo,eV),eM,gM,fu,gN,fw,fq,fy,fq,fz,fq,fA,fq),hg,_(v,hh,fj,fq,ff,_(eF,eG,eH,gH,fo,eV),eM,gM,fu,gN,fw,fq,fy,fq,fz,fq,fA,fq),hi,_(v,hj,ff,_(eF,eG,eH,gH,fo,eV)),hk,_(v,hl,eE,_(eF,eG,eH,hm),eM,gM,fu,gN),hn,_(v,ho),hp,_(v,hq,fh,_(eF,eG,eH,hr),fj,fx),hs,_(v,ht,fj,fq,ff,_(eF,eG,eH,hu),fF,_(fG,d,fH,fI,fJ,fI,fK,fI,fL,eV,eH,_(fM,fn,fN,fn,fO,fn,fP,hv)),eM,gM,fu,gN,fw,hw,fy,hw,fz,hw,fA,hw),hx,_(v,hy,eE,_(eF,eG,eH,hz)),hA,_(v,hB,ff,_(eF,eG,eH,hC)),hD,_(v,hE,ff,_(eF,hF,hG,_(eU,hH,eW,eV),hI,_(eU,hH,eW,fT),hJ,[_(eH,fg,hK,eV,fo,fT),_(eH,gv,hK,eV,fo,fT),_(eH,hL,hK,fT,fo,fT),_(eH,fg,hK,fT,fo,fT)])),hM,_(v,hN,fj,fq,ff,_(eF,eG,eH,eI)),hO,_(v,hP,fp,eD),hQ,_(v,hR,fh,_(eF,eG,eH,hm)),hS,_(v,hT,eE,_(eF,eG,eH,gs),fj,fq,ff,_(eF,eG,eH,gH,fo,eV)),hU,_(v,hV,fj,fq),hW,_(v,hX,ew,hY,eJ,gT,fj,fq,fl,eR,fm,[fn],ff,_(eF,eG,eH,gH,fo,eV),eM,gM),hZ,_(v,ia,ew,hY,eJ,ib,fj,fq,fl,eR,fm,[fn],ff,_(eF,eG,eH,gH,fo,eV),eM,gM),ic,_(v,id,ew,hY,eJ,gY,fj,fq,fl,eR,fm,[fn],ff,_(eF,eG,eH,gH,fo,eV),eM,gM),ie,_(v,ig,ew,hY,eJ,ih,fj,fq,fl,eR,fm,[fn],ff,_(eF,eG,eH,gH,fo,eV),eM,gM),ii,_(v,ij,eE,_(eF,eG,eH,hm),fj,fq)),ik,_(il,gp,im,gS,io,gP,ip,gB,iq,hh,ir,hN,is,gu,it,gA,iu,gV)));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="useLabels",o="useViews",p="loadFeedbackPlugin",q="prototypeId",r="DKHTG1",s="isAxshare",t="sitemap",u="rootNodes",v="id",w="",x="pageName",y="安装后启动",z="type",A="Folder",B="url",C="children",D="m8mdup",E="启动授权提示-ok",F="Wireframe",G="启动授权提示-ok.html",H="未登录状态",I="3ojcmq",J="注册/登录-ok",K="注册_登录-ok.html",L="已登录状态",M="首页",N="h3zsfo",O="首页-ok",P="首页-ok.html",Q="m0vbmh",R="搜索-ok",S="搜索-ok.html",T="wfdh1j",U="扫一扫*",V="扫一扫_.html",W="c5c3p7",X="收付款*",Y="收付款_.html",Z="exnrft",ba="消息提醒-ok",bb="消息提醒-ok.html",bc="快捷键区-支持6个快捷板块",bd="ez2t9r",be="慈善*",bf="慈善_.html",bg="k7rurr",bh="古兰经-ok",bi="古兰经-ok.html",bj="4uydft",bk="商城*",bl="商城_.html",bm="ajd19l",bn="资讯-ok",bo="资讯-ok.html",bp="rxbc75",bq="礼拜时间表-ok",br="礼拜时间表-ok.html",bs="uboha9",bt="日历-ok",bu="日历-ok.html",bv="gbnl38",bw="更多-ok",bx="更多-ok.html",by="麦加朝圣-ok",bz="n4z267",bA="朝觐-ok",bB="朝觐-ok.html",bC="itc59i",bD="副朝-ok",bE="副朝-ok.html",bF="nu8jwe",bG="斋月-ok",bH="斋月-ok.html",bI="2cz4bv",bJ="念珠-ok",bK="念珠-ok.html",bL="k9n2k9",bM="名言-ok",bN="名言-ok.html",bO="2cqoq8",bP="首屏广告-ok",bQ="首屏广告-ok.html",bR="adku5t",bS="头条-ok",bT="头条-ok.html",bU="9m0fpz",bV="专题-ok",bW="专题-ok.html",bX="krcz3j",bY="推荐内容-ok",bZ="推荐内容-ok.html",ca="eh3kie",cb="video-ok",cc="video-ok.html",cd="g827qo",ce="热门内容-ok",cf="热门内容-ok.html",cg="信仰",ch="pone0n",ci="默认页-ok",cj="默认页-ok.html",ck="jeopl7",cl="古兰经-ok_1.html",cm="uwy84a",cn="祷词诵读&祷告-ok",co="祷词诵读_祷告-ok.html",cp="yd4t6a",cq="礼拜时间表-ok_1.html",cr="3qza8m",cs="朝拜方向-ok",ct="朝拜方向-ok.html",cu="6emyev",cv="祷告词与雅辛-ok",cw="祷告词与雅辛-ok.html",cx="mupmro",cy="名言-ok_1.html",cz="财富",cA="我的-ok",cB="r0ul5q",cC="默认页-ok_1.html",cD="lu21z4",cE="设置-ok",cF="设置-ok.html",cG="tyz9uq",cH="阅读模式-ok",cI="阅读模式-ok.html",cJ="rcf8uy",cK="主题-ok",cL="主题-ok.html",cM="f8k8fp",cN="礼拜时间设置-ok",cO="礼拜时间设置-ok.html",cP="v55vvq",cQ="礼拜提醒-ok",cR="礼拜提醒-ok.html",cS="jg2u96",cT="文章设置-ok",cU="文章设置-ok.html",cV="v14i3p",cW="个人资料-ok",cX="个人资料-ok.html",cY="b5whba",cZ="头像-ok",da="头像-ok.html",db="b1fsev",dc="昵称-ok",dd="昵称-ok.html",de="pps8li",df="全名-ok",dg="全名-ok.html",dh="5mm3rt",di="性别-ok",dj="性别-ok.html",dk="uaa3cz",dl="电话-ok",dm="电话-ok.html",dn="7w6c9n",dp="我的地址-ok",dq="我的地址-ok.html",dr="2tyu1d",ds="NU身份认证-ok",dt="nu身份认证-ok.html",du="xy8eyk",dv="message-ok",dw="message-ok.html",dx="tsy8hs",dy="bookmark-ok",dz="bookmark-ok.html",dA="hl0m2m",dB="Ayat Al-Quran-ok",dC="ayat_al-quran-ok.html",dD="sq90g6",dE="Wirid & Doa-ok",dF="wirid___doa-ok.html",dG="qxfe7r",dH="Kegiatan-ok",dI="kegiatan-ok.html",dJ="rc0lh4",dK="Artikel-ok",dL="artikel-ok.html",dM="qbv73f",dN="bank-",dO="bank-.html",dP="1y43gu",dQ="about - ok",dR="about_-_ok.html",dS="接口文档",dT="9zfzq2",dU="日历相关",dV="日历相关.html",dW="t65b5b",dX="赞诵词（Wirid）与祷告文（Doa）",dY="赞诵词（wirid）与祷告文（doa）.html",dZ="3h1c47",ea="礼拜时间",eb="礼拜时间.html",ec="h5rdeq",ed="朝拜方向",ee="朝拜方向.html",ef="60y8id",eg="塔哈利与雅辛",eh="塔哈利与雅辛.html",ei="additionalJs",ej="plugins/debug/debug.js",ek="plugins/sitemap/sitemap.js",el="plugins/page_notes/page_notes.js",em="https://files.axshare.com/gsr/4131/scripts/hintmanager.js",en="additionalCss",eo="plugins/debug/styles/debug.css",ep="plugins/sitemap/styles/sitemap.css",eq="plugins/page_notes/styles/page_notes.css",er="globalVariables",es="onloadvariable",et="stylesheet",eu="defaultStyle",ev="627587b6038d43cca051c114ac41ad32",ew="fontName",ex="\"ArialMT\", \"Arial\", sans-serif",ey="fontWeight",ez="400",eA="fontStyle",eB="normal",eC="fontStretch",eD="5",eE="foreGroundFill",eF="fillType",eG="solid",eH="color",eI=0xFF333333,eJ="fontSize",eK="13px",eL="underline",eM="horizontalAlignment",eN="center",eO="lineSpacing",eP="characterSpacing",eQ="letterCase",eR="none",eS="strikethrough",eT="location",eU="x",eV=0,eW="y",eX="size",eY="width",eZ="height",fa="buttonSize",fb="12",fc="visible",fd="limbo",fe="baseStyle",ff="fill",fg=0xFFFFFFFF,fh="borderFill",fi=0xFF797979,fj="borderWidth",fk="1",fl="linePattern",fm="linePatternArray",fn=0,fo="opacity",fp="cornerRadius",fq="0",fr="borderVisibility",fs="top right bottom left",ft="cornerVisibility",fu="verticalAlignment",fv="middle",fw="paddingLeft",fx="2",fy="paddingTop",fz="paddingRight",fA="paddingBottom",fB="stateStyles",fC="image",fD="imageFilter",fE="rotation",fF="outerShadow",fG="on",fH="offsetX",fI=5,fJ="offsetY",fK="blurRadius",fL="spread",fM="r",fN="g",fO="b",fP="a",fQ=0.34901960784313724,fR="innerShadow",fS="textShadow",fT=1,fU=0.6470588235294118,fV="widgetBlur",fW="radius",fX=4,fY="backdropBlur",fZ="viewOverride",ga="19e82109f102476f933582835c373474",gb="transition",gc="easing",gd="duration",ge="css",gf="transform",gg="scaleX",gh="scaleY",gi="translateX",gj="translateY",gk="rotate",gl="transformOrigin",gm=50,gn="customStyles",go="box_1",gp="********************************",gq="primary_button",gr="babee1b8de0241ebaa84e103783b2fde",gs=0xFF169BD5,gt="box_2",gu="********************************",gv=0xFFF2F2F2,gw="box_3",gx="********************************",gy=0xFFD7D7D7,gz="ellipse",gA="eff044fe6497434a8c5f89f769ddde3b",gB="75a91ee5b9d042cfa01b8d565fe289c0",gC="primary_button1",gD="cd64754845384de3872fb4a066432c1f",gE=0xFF1E98D7,gF="link_button",gG="0d1f9e22da9248618edd4c1d3f726faa",gH=0xFFFFFF,gI="heading_1",gJ="1111111151944dfba49f67fd55eb1f88",gK="32px",gL="bold",gM="left",gN="top",gO="heading_2",gP="b3a15c9ddde04520be40f94c8168891e",gQ="24px",gR="heading_3",gS="8c7a4c5ad69a4369a5f7788171ac0b32",gT="18px",gU="shape",gV="55037c00beca4ab981fb8ff744aa5f75",gW="heading_4",gX="e995c891077945c89c0b5fe110d15a0b",gY="14px",gZ="heading_5",ha="386b19ef4be143bd9b6c392ded969f89",hb="heading_6",hc="fc3b9a13b5574fa098ef0a1db9aac861",hd="10px",he="label",hf="2285372321d148ec80932747449c36c9",hg="paragraph",hh="4988d43d80b44008a4a415096f1632af",hi="line",hj="619b2148ccc1497285562264d51992f9",hk="text_area",hl="42ee17691d13435b8256d8d0a814778f",hm=0xFF000000,hn="table_cell",ho="33ea2511485c479dbf973af3302f2352",hp="connector",hq="699a012e142a4bcba964d96e88b88bdf",hr=0xFF0099CC,hs="sticky_4",ht="874d265363934ac3b3d2ebd97a264a03",hu=0xFFFFB8D9,hv=0.2,hw="10",hx="form_hint",hy="4889d666e8ad4c5e81e59863039a5cc0",hz=0xFF999999,hA="form_disabled",hB="9bd0236217a94d89b0314c8c7fc75f16",hC=0xFFF0F0F0,hD="flow_shape",hE="df01900e3c4e43f284bafec04b0864c4",hF="linearGradient",hG="startPoint",hH=0.5,hI="endPoint",hJ="stops",hK="offset",hL=0xFFE4E4E4,hM="icon",hN="5a30893901354bfe85953fed02e280f5",hO="button",hP="5db5ef5901ff4787983664eeb82fb9ae",hQ="line1",hR="a15912cc2ad644f6a3a4ec74f3a94f43",hS="link_button1",hT="63327d0724c24267ae14f7b572b821e4",hU="_图片",hV="a8350516c1f14dacb6ede5ed0f0c3703",hW="_36号字_顶部标题、大按钮、弹窗提示主标题_",hX="d95af4455a714bb8b3e841588eb805b4",hY="\"PingFangSC-Regular\", \"PingFang SC\", sans-serif",hZ="_32号字_单行列表内，右方操作说明的文字__）",ia="27636afc631f451794153662ad141f01",ib="16px",ic="_28号字_页面备注信息及列表的表头说明文字__）",id="cc88142b5cc246958c2e5bdbd3679cf7",ie="_24号字_最小说明文本，例如列表时间态、版权信息等）",ig="88c6291bc3a544c287bc9a0189476d86",ih="12px",ii="image1",ij="238b3961cc1946e5a8bb8126230ab73b",ik="duplicateStyles",il="779663ba55f948c0ab8ce3fe65e4acfa",im="aa017f6a23d447e8a77c4c2eea3d335c",io="da737bfd44c542f1b9405d40ba3ddd50",ip="f87696b9f1cf4a86a16e8969cb6512bd",iq="eb2aa8c4bd10412aa7fd73b94b47ccae",ir="26c731cb771b44a88eb8b6e97e78c80e",is="f6649069e9b046cba0fa5f3f2fe0d377",it="937c86b443f84535885a11907582500f",iu="b9f8dbd3e90c42eab84fb3673402360f";
return _creator();
})());