body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-107px;
  width:3368px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:107px;
  top:96px;
  width:329px;
  height:624px;
  display:flex;
  transition:none;
}
#u0 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u0_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:624px;
}
#u0_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:408px;
  width:62px;
  height:75px;
  display:flex;
  transition:none;
}
#u1 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:75px;
}
#u1_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:736px;
  top:96px;
  width:287px;
  height:840px;
  display:flex;
  transition:none;
}
#u2 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:287px;
  height:840px;
}
#u2_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:1325px;
  top:96px;
  width:321px;
  height:618px;
  display:flex;
  transition:none;
}
#u3 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:321px;
  height:618px;
}
#u3_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:1946px;
  top:96px;
  width:321px;
  height:620px;
  display:flex;
  transition:none;
}
#u4 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:321px;
  height:620px;
}
#u4_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:2603px;
  top:98px;
  width:321px;
  height:618px;
  display:flex;
  transition:none;
}
#u5 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:321px;
  height:618px;
}
#u5_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:2603px;
  top:772px;
  width:322px;
  height:618px;
  display:flex;
  transition:none;
}
#u6 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:322px;
  height:618px;
}
#u6_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:1325px;
  top:800px;
  width:323px;
  height:620px;
  display:flex;
  transition:none;
}
#u7 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:620px;
}
#u7_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:928px;
  top:136px;
  width:37px;
  height:43px;
  display:flex;
  transition:none;
}
#u8 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:43px;
}
#u8_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:758px;
  top:446px;
  width:243px;
  height:28px;
  display:flex;
  transition:none;
}
#u9 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u9_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:243px;
  height:28px;
}
#u9_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u10_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:105px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:436px;
  top:146px;
  width:300px;
  height:105px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u10 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u10_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:436px;
  top:96px;
  width:268px;
  height:32px;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:-13px;
  top:13px;
  width:32px;
  height:6px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
  transition:none;
}
#u12 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12_img {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-3px;
  width:39px;
  height:13px;
}
#u12_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:262px;
  height:32px;
  background:inherit;
  background-color:rgba(33, 98, 243, 0.2784313725490196);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:0px;
  width:262px;
  height:32px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u13 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u14_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC", sans-serif;
  font-style:normal;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:454px;
  top:102px;
  width:158px;
  height:24px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC", sans-serif;
  font-style:normal;
}
#u14 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u14_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:436px;
  top:303px;
  width:282px;
  height:60px;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u16 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u16_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u16_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:98px;
  top:0px;
  width:184px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u17 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u17_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:184px;
  height:30px;
}
#u17_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:98px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u18 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u18_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u18_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:98px;
  top:30px;
  width:184px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u19 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u19_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:184px;
  height:30px;
}
#u19_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u20_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC", sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:14px;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:436px;
  top:272px;
  width:70px;
  height:19px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC", sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:14px;
}
#u20 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u20_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u21_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:172px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:1025px;
  top:146px;
  width:300px;
  height:172px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u21 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u21_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:1025px;
  top:96px;
  width:268px;
  height:32px;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:-13px;
  top:13px;
  width:32px;
  height:6px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
  transition:none;
}
#u23 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u23_img {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-3px;
  width:39px;
  height:13px;
}
#u23_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u24_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:262px;
  height:32px;
  background:inherit;
  background-color:rgba(33, 98, 243, 0.2784313725490196);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:0px;
  width:262px;
  height:32px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u24 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u24_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u25_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC", sans-serif;
  font-style:normal;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:1043px;
  top:102px;
  width:194px;
  height:24px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC", sans-serif;
  font-style:normal;
}
#u25 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u25_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:758px;
  top:576px;
  width:243px;
  height:28px;
  display:flex;
  transition:none;
}
#u26 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u26_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:243px;
  height:28px;
}
#u26_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u27_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:1025px;
  top:348px;
  width:195px;
  height:33px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u27 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u27_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u28_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:87px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:1025px;
  top:386px;
  width:300px;
  height:87px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u28 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u28_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u29_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:1025px;
  top:494px;
  width:65px;
  height:33px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
}
#u29 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u29_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u30_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:87px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:1025px;
  top:532px;
  width:300px;
  height:87px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u30 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u30_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u31_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:137px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:1646px;
  top:136px;
  width:300px;
  height:137px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u31 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u31_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u32_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:1646px;
  top:321px;
  width:300px;
  height:60px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u32 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u32_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:1646px;
  top:292px;
  width:59px;
  height:15px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u33 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u34_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:152px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:2267px;
  top:136px;
  width:300px;
  height:152px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u34 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u34_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u35_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:104px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:2924px;
  top:136px;
  width:300px;
  height:104px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u35 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u35_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u36_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:2267px;
  top:289px;
  width:300px;
  height:115px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u36 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u36_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u37_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:52px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:2925px;
  top:800px;
  width:300px;
  height:52px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u37 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u37_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u38_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:261px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:2925px;
  top:877px;
  width:300px;
  height:261px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u38 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u38_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u39_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:336px;
  height:152px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:2267px;
  top:406px;
  width:336px;
  height:152px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u39 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u39_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:947px;
  top:136px;
  width:0px;
  height:0px;
  transition:none;
}
#u40_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-61px;
  width:10px;
  height:61px;
}
#u40_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-61px;
  width:549px;
  height:10px;
}
#u40_seg2 {
  border-width:0px;
  position:absolute;
  left:534px;
  top:-61px;
  width:10px;
  height:21px;
}
#u40_seg3 {
  border-width:0px;
  position:absolute;
  left:529px;
  top:-52px;
  width:20px;
  height:20px;
}
#u40_text {
  border-width:0px;
  position:absolute;
  left:200px;
  top:-64px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:1580px;
  top:146px;
  width:37px;
  height:43px;
  display:flex;
  transition:none;
}
#u41 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u41_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:43px;
}
#u41_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:1600px;
  top:146px;
  width:0px;
  height:0px;
  transition:none;
}
#u42_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-71px;
  width:10px;
  height:71px;
}
#u42_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-71px;
  width:517px;
  height:10px;
}
#u42_seg2 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:-71px;
  width:10px;
  height:21px;
}
#u42_seg3 {
  border-width:0px;
  position:absolute;
  left:497px;
  top:-62px;
  width:20px;
  height:20px;
}
#u42_text {
  border-width:0px;
  position:absolute;
  left:178px;
  top:-74px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:1987px;
  top:213px;
  width:239px;
  height:43px;
  display:flex;
  transition:none;
}
#u43 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u43_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:239px;
  height:43px;
}
#u43_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:1987px;
  top:262px;
  width:239px;
  height:28px;
  display:flex;
  transition:none;
}
#u44 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u44_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:239px;
  height:28px;
}
#u44_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:2226px;
  top:235px;
  width:0px;
  height:0px;
  transition:none;
}
#u45_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:40px;
  height:10px;
}
#u45_seg1 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:-160px;
  width:10px;
  height:165px;
}
#u45_seg2 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:-160px;
  width:513px;
  height:10px;
}
#u45_seg3 {
  border-width:0px;
  position:absolute;
  left:533px;
  top:-160px;
  width:10px;
  height:23px;
}
#u45_seg4 {
  border-width:0px;
  position:absolute;
  left:528px;
  top:-149px;
  width:20px;
  height:20px;
}
#u45_text {
  border-width:0px;
  position:absolute;
  left:150px;
  top:-163px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:2107px;
  top:290px;
  width:0px;
  height:0px;
  transition:none;
}
#u46_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:442px;
}
#u46_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:432px;
  width:667px;
  height:10px;
}
#u46_seg2 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:432px;
  width:10px;
  height:50px;
}
#u46_seg3 {
  border-width:0px;
  position:absolute;
  left:647px;
  top:470px;
  width:20px;
  height:20px;
}
#u46_text {
  border-width:0px;
  position:absolute;
  left:82px;
  top:429px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:3075px;
  top:1504px;
  width:400px;
  height:32px;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:31px;
  width:400px;
  height:1px;
  display:flex;
  transition:none;
}
#u48 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u48_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:401px;
  height:2px;
}
#u48_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u49_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
}
#u49 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u49_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:758px;
  top:706px;
  width:243px;
  height:49px;
  display:flex;
  transition:none;
}
#u50 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u50_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:243px;
  height:49px;
}
#u50_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:1001px;
  top:731px;
  width:0px;
  height:0px;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u51_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:491px;
  height:10px;
}
#u51_seg1 {
  border-width:0px;
  position:absolute;
  left:481px;
  top:-5px;
  width:10px;
  height:74px;
}
#u51_seg2 {
  border-width:0px;
  position:absolute;
  left:476px;
  top:57px;
  width:20px;
  height:20px;
}
#u51_text {
  border-width:0px;
  position:absolute;
  left:228px;
  top:-9px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:1361px;
  top:851px;
  width:41px;
  height:49px;
  display:flex;
  transition:none;
}
#u52 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u52_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:49px;
}
#u52_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:1361px;
  top:876px;
  width:0px;
  height:0px;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u53_seg0 {
  border-width:0px;
  position:absolute;
  left:-204px;
  top:-5px;
  width:204px;
  height:10px;
}
#u53_seg1 {
  border-width:0px;
  position:absolute;
  left:-204px;
  top:-5px;
  width:10px;
  height:122px;
}
#u53_seg2 {
  border-width:0px;
  position:absolute;
  left:-486px;
  top:107px;
  width:292px;
  height:10px;
}
#u53_seg3 {
  border-width:0px;
  position:absolute;
  left:-486px;
  top:60px;
  width:10px;
  height:57px;
}
#u53_seg4 {
  border-width:0px;
  position:absolute;
  left:-491px;
  top:52px;
  width:20px;
  height:20px;
}
#u53_text {
  border-width:0px;
  position:absolute;
  left:-260px;
  top:103px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u54_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:1646px;
  top:398px;
  width:51px;
  height:15px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u54 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u54_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
