package islamic

import (
	"context"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/app/islamic-content-svc/internal/service"
	"strconv"
)

type (
	sIslamic struct {
		//signInMsgChan        chan *model.SignInLogInput
		//attrsBatchUpdateChan chan *model.AttrsToUpdate
		//attrsNoDelayChan     chan *model.AttrsToUpdate
		//quit                 chan struct{}
		//
		//ConfigCaptcha    string
		//signInRecord<PERSON>han chan *do.UserSigninLog
		//accountSet       *bloom.Filter
		//transferSet      *bloom.Filter
		//phoneSet         *bloom.Filter
	}
)

func init() {
	service.RegisterIslamic(New())
}

func New() service.IIslamic {
	u := &sIslamic{
		//signInMsgChan:        make(chan *model.SignInLogInput, 1000),
		//attrsBatchUpdateChan: make(chan *model.AttrsToUpdate, 10000),
		//attrsNoDelayChan:     make(chan *model.AttrsToUpdate, 100),
		//
		//signInRecordChan: make(chan *do.UserSigninLog, 1000),
		//quit:             make(chan struct{}, 1),
	}
	//u.cronJobCreateSignInLog()
	//u.cronJobUpdateUserAttrs()
	//u.cronJobUpdateUserAttrsNoDelay()

	//u.ConfigCaptcha = u.getSmsOptConfig(context.Background())

	//g.Go(gctx.New(), func(ctx context.Context) {
	//	time.Sleep(3 * time.Second)
	//	// u.convertTopLevelDomain(ctx)
	//}, nil)
	return u
}

func (s *sIslamic) SurahList(ctx context.Context, in *model.SurahParamInput) (out []*model.SurahParamOutput) {
	var quranList []*entity.SuratDaftar
	query := dao.SuratDaftar.Ctx(ctx)
	if in.Id > 0 {
		query = query.Where(dao.SuratDaftar.Columns().Id, in.Id)
	}
	if in.Name != "" {
		query = query.WhereLike(dao.SuratDaftar.Columns().NamaLatin, "%"+in.Name+"%")
	}
	err := query.Scan(&quranList)
	if err != nil || len(quranList) == 0 {
		return out
	}
	for _, quran := range quranList {
		one := &model.SurahParamOutput{
			Id:          uint(quran.Id),
			Name:        quran.Nama,
			NameLatin:   quran.NamaLatin,
			JumlahAyat:  quran.JumlahAyat,
			Arti:        quran.Arti,
			TempatTurun: quran.TempatTurun,
			Nomor:       quran.Nomor,
		}
		out = append(out, one)
	}
	return out
}

func (s *sIslamic) JuzList(ctx context.Context, in *model.JuzParamInput) (out []*model.JuzParamOutput) {

	var surahList []*entity.SuratDaftar
	querySurah := dao.SuratDaftar.Ctx(ctx)
	errSurah := querySurah.Scan(&surahList)
	if errSurah != nil || len(surahList) == 0 {
		return out
	}
	// 初始化一个map 获取所有的SurahId和SurahName
	surahMap := make(map[int]*model.SurahParamOutput)
	for _, surah := range surahList {
		surahMap[surah.Id] = &model.SurahParamOutput{
			Name:      surah.Nama,
			NameLatin: surah.NamaLatin,
		}
	}

	var ayahList []*entity.SuratAyat
	query := dao.SuratAyat.Ctx(ctx)

	if in.Name != "" {
	}
	err := query.Scan(&ayahList)
	if err != nil || len(ayahList) == 0 {
		return out
	}

	//根据quran.Juz进行分组
	juzMap := make(map[int]*v1.JuzInfo)
	for _, quran := range ayahList {
		if _, ok := juzMap[quran.Juz]; !ok {
			juzMap[quran.Juz] = &v1.JuzInfo{
				StartSurahId:   int32(quran.SurahId),
				StartSurahName: surahMap[quran.SurahId].NameLatin,
				EndSurahId:     int32(quran.SurahId),
				EndSurahName:   surahMap[quran.SurahId].NameLatin,
				StartAyahId:    int32(quran.AyatId),
				EndAyahId:      int32(quran.AyatId),
				FirstWord:      quran.Ar,
			}
		} else {
			//更新EndSurahId和EndAyahId
			juzMap[quran.Juz].EndSurahId = int32(quran.SurahId)
			juzMap[quran.Juz].EndSurahName = ""
			juzMap[quran.Juz].EndAyahId = int32(quran.AyatId)
		}
	}
	//juzMap 排序
	for key, juz := range juzMap {
		out = append(out, &model.JuzParamOutput{
			StartSurahId:   uint(juz.StartSurahId),
			StartSurahName: juz.StartSurahName,
			EndSurahId:     uint(juz.EndSurahId),
			EndSurahName:   juz.EndSurahName,
			StartAyahId:    uint(juz.StartAyahId),
			EndAyahId:      uint(juz.EndAyahId),
			Name:           "Juz " + strconv.Itoa(key+1), // 这里可以根据需要设置Juz的名称
			FirstWord:      juz.FirstWord,
		})
	}
	return out
}

func (s *sIslamic) AyahList(ctx context.Context, in *model.AyahParamInput) (out []*model.AyahParamOutPut) {
	var ayahList []*entity.SuratAyat

	query := dao.SuratAyat.Ctx(ctx)
	if in.Id > 0 {
		query = query.Where(dao.SuratAyat.Columns().Id, in.Id)
	}
	if gconv.Int(in.SurahId) > 0 {
		query = query.Where(dao.SuratAyat.Columns().SurahId, in.SurahId)
	}
	if gconv.Int(in.JuzId) > 0 {
		query = query.Where(dao.SuratAyat.Columns().Juz, in.JuzId)
	}
	if gconv.Int(in.Page) > 0 {
		query = query.Where(dao.SuratAyat.Columns().Page, in.Page)
	}

	err := query.Scan(&ayahList)
	if err != nil || len(ayahList) == 0 {
		return out
	}
	for _, ayah := range ayahList {
		one := &model.AyahParamOutPut{
			Id:      uint(ayah.Id),
			SurahId: uint(ayah.SurahId),
			Tr:      ayah.Tr,
			Ar:      ayah.Ar,
			Idn:     ayah.Idn,
			Nomor:   uint(ayah.Nomor),
			Juz:     uint(ayah.Juz),
			Page:    uint(ayah.Page),
		}
		out = append(out, one)
	}
	return out
}
