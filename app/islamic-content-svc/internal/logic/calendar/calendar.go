package calendar

import (
	"context"
	"fmt"
	"time"

	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/app/islamic-content-svc/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type sCalendar struct{}

func init() {
	service.RegisterCalendar(New())
}

func New() service.ICalendar {
	return &sCalendar{}
}

var (
	hijriahCl = dao.CalendarHijriah.Columns()
	eventsCl  = dao.CalendarEvents.Columns()
)

// GetCalendar 获取日历数据
func (s *sCalendar) GetCalendar(ctx context.Context, req *v1.CalendarReq) ([]*model.CalendarDateInfo, error) {
	// 参数验证
	if req.Year <= 0 || req.Month <= 0 || req.Month > 12 {
		return nil, fmt.Errorf("invalid year or month: year=%d, month=%d", req.Year, req.Month)
	}

	// 设置默认值
	methodCode := req.MethodCode
	if methodCode == "" || methodCode == "AUTO" {
		methodCode = "UMMUL_QURA"
	}
	dateAdjustment := req.DateAdjustment

	// 获取该月的所有日期
	startDate := gtime.NewFromStr(fmt.Sprintf("%d-%02d-01", req.Year, req.Month))
	daysInMonth := startDate.DaysInMonth()

	var result []*model.CalendarDateInfo

	for day := 1; day <= daysInMonth; day++ {
		// 获取或计算Hijriah日期
		hijriahInfo, err := s.getHijriahDate(ctx, int32(req.Year), int32(req.Month), int32(day), methodCode, dateAdjustment)
		if err != nil {
			g.Log().Error(ctx, "获取Hijriah日期失败:", err)
			continue
		}

		// 获取当日事件
		events, err := s.getEventsForDate(ctx, int32(req.Year), int32(req.Month), int32(day))
		if err != nil {
			g.Log().Error(ctx, "获取事件失败:", err)
			// 事件获取失败不影响日历显示，继续处理
		}

		// 构建日历日期信息
		dateInfo := &model.CalendarDateInfo{
			GregorianYear:  int32(req.Year),
			GregorianMonth: int32(req.Month),
			GregorianDay:   int32(day),
			HijriahYear:    int32(hijriahInfo.HijriahYear),
			HijriahMonth:   int32(hijriahInfo.HijriahMonth),
			HijriahDay:     int32(hijriahInfo.HijriahDay),
			MethodCode:     methodCode,
			Weekday:        int32(hijriahInfo.Weekday),
			Pasaran:        int32(hijriahInfo.Pasaran),
			WeekdayName:    s.getWeekdayName(int32(hijriahInfo.Weekday)),
			PasaranName:    s.getPasaranName(int32(hijriahInfo.Pasaran)),
			Events:         events,
		}

		result = append(result, dateInfo)
	}

	return result, nil
}

// getHijriahDate 获取或计算Hijriah日期
func (s *sCalendar) getHijriahDate(ctx context.Context, year, month, day int32, methodCode string, adjustment int32) (*entity.CalendarHijriah, error) {
	// 先从数据库查询
	var hijriahData *entity.CalendarHijriah
	err := dao.CalendarHijriah.Ctx(ctx).
		Where(hijriahCl.GregorianYear, year).
		Where(hijriahCl.GregorianMonth, month).
		Where(hijriahCl.GregorianDay, day).
		Where(hijriahCl.MethodCode, methodCode).
		Scan(&hijriahData)

	if err == nil && hijriahData != nil {
		// 应用日期校正
		if adjustment != 0 {
			hijriahData.HijriahDay += int(adjustment)
			// 这里简化处理，实际应该考虑月份边界
		}
		return hijriahData, nil
	}

	// 数据库中没有，使用计算方式（仅支持UMMUL_QURA）
	if methodCode == "UMMUL_QURA" {
		return s.calculateUmmulQura(year, month, day, adjustment)
	}

	return nil, fmt.Errorf("unsupported method code: %s", methodCode)
}

// calculateUmmulQura 使用Ummul Qura方法计算Hijriah日期
func (s *sCalendar) calculateUmmulQura(year, month, day int32, adjustment int32) (*entity.CalendarHijriah, error) {
	// 简化的Hijriah日期计算（实际项目中应使用专业的转换库）
	gregorianDate := time.Date(int(year), time.Month(month), int(day), 0, 0, 0, 0, time.UTC)

	// 使用简化算法计算Hijriah日期（这里只是示例，实际应使用准确的算法）
	// Hijriah历法大约比公历早11天每年
	julianDay := s.gregorianToJulian(int(year), int(month), int(day))
	hijriahJulian := julianDay - 1948439 // 大概的转换基数
	hijriahYear := int(hijriahJulian/354) + 1
	hijriahMonth := int((hijriahJulian%354)/29) + 1
	hijriahDay := int(hijriahJulian%29) + 1

	// 应用日期校正
	if adjustment != 0 {
		hijriahDay += int(adjustment)
		// 简化处理，不考虑月份边界
	}

	// 计算星期和Pasaran
	weekday := int(gregorianDate.Weekday())
	pasaran := s.calculatePasaran(gregorianDate)

	return &entity.CalendarHijriah{
		GregorianYear:  int(year),
		GregorianMonth: int(month),
		GregorianDay:   int(day),
		HijriahYear:    hijriahYear,
		HijriahMonth:   hijriahMonth,
		HijriahDay:     hijriahDay,
		MethodCode:     "UMMUL_QURA",
		Weekday:        weekday,
		Pasaran:        pasaran,
	}, nil
}

// gregorianToJulian 公历转儒略日
func (s *sCalendar) gregorianToJulian(year, month, day int) int {
	if month <= 2 {
		year--
		month += 12
	}
	a := year / 100
	b := 2 - a + a/4
	return int(365.25*float64(year+4716)) + int(30.6001*float64(month+1)) + day + b - 1524
}

// calculatePasaran 计算Java Pasaran（5日循环）
func (s *sCalendar) calculatePasaran(date time.Time) int {
	// Java历法的Pasaran是5日循环：Legi, Pahing, Pon, Wage, Kliwon
	// 使用1900年1月1日作为基准日（假设为Legi=0）
	baseDate := time.Date(1900, 1, 1, 0, 0, 0, 0, time.UTC)
	days := int(date.Sub(baseDate).Hours() / 24)
	return days % 5
}

// getEventsForDate 获取指定日期的事件
func (s *sCalendar) getEventsForDate(ctx context.Context, year, month, day int32) ([]*model.CalendarEventInfo, error) {
	var events []*entity.CalendarEvents
	err := dao.CalendarEvents.Ctx(ctx).
		Where(eventsCl.GregorianYear, year).
		Where(eventsCl.GregorianMonth, month).
		Where(eventsCl.GregorianDay, day).
		Scan(&events)

	if err != nil {
		return nil, err
	}

	var result []*model.CalendarEventInfo
	for _, event := range events {
		eventInfo := &model.CalendarEventInfo{
			Id:            int64(event.Id),
			EventType:     event.EventType,
			Title:         event.Title,
			Description:   event.Description,
			JumpUrl:       event.JumpUrl,
			EventTypeName: s.getEventTypeName(event.EventType),
		}
		result = append(result, eventInfo)
	}

	return result, nil
}

// getWeekdayName 获取星期名称
func (s *sCalendar) getWeekdayName(weekday int32) string {
	weekdayNames := []string{"Ahad", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu"}
	if weekday >= 0 && weekday < int32(len(weekdayNames)) {
		return weekdayNames[weekday]
	}
	return "Unknown"
}

// getPasaranName 获取Pasaran名称
func (s *sCalendar) getPasaranName(pasaran int32) string {
	pasaranNames := []string{"Legi", "Pahing", "Pon", "Wage", "Kliwon"}
	if pasaran >= 0 && pasaran < int32(len(pasaranNames)) {
		return pasaranNames[pasaran]
	}
	return "Unknown"
}

// getEventTypeName 获取事件类型名称
func (s *sCalendar) getEventTypeName(eventType string) string {
	switch eventType {
	case "HARI_BESAR":
		return "Hari Besar Islam"
	case "LIBUR_NASIONAL":
		return "Libur Nasional"
	case "PUASA":
		return "Hari Puasa"
	default:
		return eventType
	}
}

// TestHelper 用于测试的公开方法
func TestHelper() *sCalendar {
	return &sCalendar{}
}
