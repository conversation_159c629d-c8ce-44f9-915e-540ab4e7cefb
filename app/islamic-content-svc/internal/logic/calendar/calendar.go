package calendar

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/service"
)

type sCalendar struct{}

func init() {
	service.RegisterCalendar(New())
}

func New() service.ICalendar {
	return &sCalendar{}
}

func (s *sCalendar) GetCalendar(ctx context.Context, req *v1.CalendarReq) (res *v1.CalendarRes, err error) {

}
