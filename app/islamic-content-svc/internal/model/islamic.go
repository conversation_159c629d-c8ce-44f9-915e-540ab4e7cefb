package model

type SurahParamInput struct {
	Id   uint   `json:"id"                    dc:"table id"`
	Name string `json:"name"                  dc:"name"`
}

type SurahParamOutput struct {
	Id          uint   `json:"id"                    dc:"table id"`
	Name        string `json:"name"                  dc:"name"`
	NameLatin   string `json:"name_latin"                  dc:"name_latin"`
	JumlahAyat  int    `json:"jumlah_ayat"                  dc:"章节编号 (1-114)"`
	Nomor       int    `json:"nomor"                  dc:"经文数量"`
	Arti        string `json:"arti"                  dc:"章节描述"`
	TempatTurun string `json:"tempat_turun"                  dc:"降示地点"`
}

type JuzParamInput struct {
	Name string `json:"name"                  dc:"name"`
}

type JuzParamOutput struct {
	StartSurahId   uint   `json:"start_surah_id"                    dc:"开始章id"`
	StartSurahName string `json:"start_surah_name"                  dc:"开始章name"`
	EndSurahId     uint   `json:"end_surah_name"                  dc:"结束章name"`
	EndSurahName   string `json:"end_surah_id"                  dc:"结束章id"`
	StartAyahId    uint   `json:"start_ayah_id"                  dc:"节id"`
	EndAyahId      uint   `json:"end_ayah_id"                  dc:"节id"`
	Name           string `json:"name"                  dc:"juz名称"`
	FirstWord      string `json:"first_word"                  dc:"page 对应经文的第一个单词"`
}

type AyahParamInput struct {
	Id      uint32 `json:"id"                  dc:"id"`
	SurahId uint32 `json:"surah_id"                  dc:"章节id"`
	JuzId   uint32 `json:"juz_id"                  dc:"juz_id"`
	Page    uint32 `json:"page"                  dc:"page 页数量"`
}

type AyahParamOutPut struct {
	Id      uint   `json:"id"                    dc:"table id"`
	SurahId uint   `json:"surah_id"                  dc:"所属章节ID"`
	Nomor   uint   `json:"nomor"                  dc:"经文在章节中的编号"`
	Ar      string `json:"arti"                  dc:"阿拉伯语经文"`
	Tr      string `json:"tr"                  dc:"音译文本"`
	Idn     string `json:"idn"                  dc:"印尼语翻译"`
	Juz     uint   `json:"Juz"                  dc:"Juz"`
	Page    uint   `json:"page"                  dc:"page 页数量"`
}

// Banner相关的内部模型结构

// BannerInfo Banner信息输出结构
type BannerInfo struct {
	Id          uint32 `json:"id" dc:"Banner ID"`
	LanguageId  uint32 `json:"language_id" dc:"语言ID"`
	Title       string `json:"title" dc:"广告标题"`
	Description string `json:"description" dc:"广告描述"`
	ImageUrl    string `json:"image_url" dc:"广告图片URL"`
	LinkUrl     string `json:"link_url" dc:"跳转链接URL"`
	SortOrder   uint32 `json:"sort_order" dc:"排序权重"`
}

// BannerClickInput Banner点击统计输入参数
type BannerClickInput struct {
	BannerId uint32 `json:"banner_id" dc:"Banner ID"`
	DeviceId string `json:"device_id" dc:"设备唯一标识"`
}

// Calendar相关的内部模型结构

// CalendarDateInfo 日历日期信息输出结构
type CalendarDateInfo struct {
	GregorianYear  int32                `json:"gregorian_year" dc:"公历年"`
	GregorianMonth int32                `json:"gregorian_month" dc:"公历月"`
	GregorianDay   int32                `json:"gregorian_day" dc:"公历日"`
	HijriahYear    int32                `json:"hijriah_year" dc:"Hijriah年"`
	HijriahMonth   int32                `json:"hijriah_month" dc:"Hijriah月"`
	HijriahDay     int32                `json:"hijriah_day" dc:"Hijriah日"`
	MethodCode     string               `json:"method_code" dc:"计算方法代码"`
	Weekday        int32                `json:"weekday" dc:"星期：0-6"`
	Pasaran        int32                `json:"pasaran" dc:"Pasaran：0-4"`
	WeekdayName    string               `json:"weekday_name" dc:"星期名称"`
	PasaranName    string               `json:"pasaran_name" dc:"Pasaran名称"`
	Events         []*CalendarEventInfo `json:"events" dc:"当日事件列表"`
}

// CalendarEventInfo 日历事件信息输出结构
type CalendarEventInfo struct {
	Id            int64  `json:"id" dc:"事件ID"`
	EventType     string `json:"event_type" dc:"事件类型"`
	Title         string `json:"title" dc:"事件标题"`
	Description   string `json:"description" dc:"事件描述"`
	JumpUrl       string `json:"jump_url" dc:"点击跳转链接"`
	EventTypeName string `json:"event_type_name" dc:"事件类型名称"`
}
