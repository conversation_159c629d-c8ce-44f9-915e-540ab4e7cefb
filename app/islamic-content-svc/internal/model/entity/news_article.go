// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// NewsArticle is the golang structure for table news_article.
type NewsArticle struct {
	Id          uint   `json:"id"          orm:"id"           description:""`               //
	LanguageZh  uint   `json:"languageZh"  orm:"language_zh"  description:"语言_zh，1启用，0关闭"`  // 语言_zh，1启用，0关闭
	LanguageEn  uint   `json:"languageEn"  orm:"language_en"  description:"语言_en，1启用，0关闭"`  // 语言_en，1启用，0关闭
	LanguageId  uint   `json:"languageId"  orm:"language_id"  description:"语言id，1启用，0关闭"`   // 语言id，1启用，0关闭
	CategoryId  uint   `json:"categoryId"  orm:"category_id"  description:"分类id"`           // 分类id
	AdminId     uint   `json:"adminId"     orm:"admin_id"     description:"分类负责人id"`        // 分类负责人id
	CoverImgs   string `json:"coverImgs"   orm:"cover_imgs"   description:"专题图片"`           // 专题图片
	Creater     uint   `json:"creater"     orm:"creater"      description:"创建者id"`          // 创建者id
	CreateName  string `json:"createName"  orm:"create_name"  description:"后台创建者"`          // 后台创建者
	Author      string `json:"author"      orm:"author"       description:"创建人"`            // 创建人
	IsTop       uint   `json:"isTop"       orm:"is_top"       description:"是否加入头条，1启用，0关闭"` // 是否加入头条，1启用，0关闭
	IsRecommend uint   `json:"isRecommend" orm:"is_recommend" description:"是否推荐，1启用，0关闭"`   // 是否推荐，1启用，0关闭
	IsPublish   uint   `json:"isPublish"   orm:"is_publish"   description:"是否发布，1启用，0关闭"`   // 是否发布，1启用，0关闭
	IsDraft     uint   `json:"isDraft"     orm:"is_draft"     description:"是否草稿状态，1是，0否"`   // 是否草稿状态，1是，0否
	CreateTime  int64  `json:"createTime"  orm:"create_time"  description:"创建时间"`           // 创建时间
	PublishTime int64  `json:"publishTime" orm:"publish_time" description:"发布时间"`           // 发布时间
	UpdateTime  int64  `json:"updateTime"  orm:"update_time"  description:"修改时间"`           // 修改时间
	DeleteTime  int64  `json:"deleteTime"  orm:"delete_time"  description:"删除时间"`           // 删除时间
}
