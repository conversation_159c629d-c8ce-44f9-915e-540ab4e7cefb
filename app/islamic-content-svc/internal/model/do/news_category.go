// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsCategory is the golang structure of table news_category for DAO operations like Where/Data.
type NewsCategory struct {
	g.Meta     `orm:"table:news_category, do:true"`
	Id         interface{} //
	ParentId   interface{} // 上级id，0表示顶级
	Status     interface{} // 状态，1启用，0关闭
	Sort       interface{} // 排序，数字越小，排序越靠前
	AdminId    interface{} // 分类负责人id
	CoverImgs  interface{} // 封面图
	Remark     interface{} // 备注
	Creater    interface{} // 创建者id
	CreateName interface{} // 创建者
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 修改时间
	DeleteTime interface{} // 删除时间
}
