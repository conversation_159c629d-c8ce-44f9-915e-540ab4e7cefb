// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsTopic is the golang structure of table news_topic for DAO operations like Where/Data.
type NewsTopic struct {
	g.Meta     `orm:"table:news_topic, do:true"`
	Id         interface{} //
	Counts     interface{} // 文章数量
	Status     interface{} // 是否显示，1启用，0关闭
	Sort       interface{} // 排序，数字越小，排序越靠前
	AdminId    interface{} // 分类负责人id
	TopicImgs  interface{} // 专题图片
	Creater    interface{} // 创建者id
	CreateName interface{} // 创建者
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 修改时间
	DeleteTime interface{} // 删除时间
}
