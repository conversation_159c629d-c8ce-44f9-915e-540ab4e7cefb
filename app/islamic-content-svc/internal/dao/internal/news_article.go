// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticleDao is the data access object for the table news_article.
type NewsArticleDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  NewsArticleColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// NewsArticleColumns defines and stores column names for the table news_article.
type NewsArticleColumns struct {
	Id          string //
	LanguageZh  string // 语言_zh，1启用，0关闭
	LanguageEn  string // 语言_en，1启用，0关闭
	LanguageId  string // 语言id，1启用，0关闭
	CategoryId  string // 分类id
	AdminId     string // 分类负责人id
	CoverImgs   string // 专题图片
	Creater     string // 创建者id
	CreateName  string // 后台创建者
	Author      string // 创建人
	IsTop       string // 是否加入头条，1启用，0关闭
	IsRecommend string // 是否推荐，1启用，0关闭
	IsPublish   string // 是否发布，1启用，0关闭
	IsDraft     string // 是否草稿状态，1是，0否
	CreateTime  string // 创建时间
	PublishTime string // 发布时间
	UpdateTime  string // 修改时间
	DeleteTime  string // 删除时间
}

// newsArticleColumns holds the columns for the table news_article.
var newsArticleColumns = NewsArticleColumns{
	Id:          "id",
	LanguageZh:  "language_zh",
	LanguageEn:  "language_en",
	LanguageId:  "language_id",
	CategoryId:  "category_id",
	AdminId:     "admin_id",
	CoverImgs:   "cover_imgs",
	Creater:     "creater",
	CreateName:  "create_name",
	Author:      "author",
	IsTop:       "is_top",
	IsRecommend: "is_recommend",
	IsPublish:   "is_publish",
	IsDraft:     "is_draft",
	CreateTime:  "create_time",
	PublishTime: "publish_time",
	UpdateTime:  "update_time",
	DeleteTime:  "delete_time",
}

// NewNewsArticleDao creates and returns a new DAO object for table data access.
func NewNewsArticleDao(handlers ...gdb.ModelHandler) *NewsArticleDao {
	return &NewsArticleDao{
		group:    "default",
		table:    "news_article",
		columns:  newsArticleColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NewsArticleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NewsArticleDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NewsArticleDao) Columns() NewsArticleColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NewsArticleDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NewsArticleDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NewsArticleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
