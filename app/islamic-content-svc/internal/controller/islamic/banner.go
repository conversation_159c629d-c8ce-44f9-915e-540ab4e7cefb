package islamic

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type ControllerBanner struct {
	v1.UnimplementedBannerServiceServer
}

func (*ControllerBanner) BannerList(ctx context.Context, req *v1.BannerListReq) (res *v1.BannerListRes, err error) {

	bannerInfos, err := service.Banner().BannerList(ctx, req.LanguageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// use gconv.Structs to convert bannerList
	bannerList := make([]*v1.BannerInfo, 0, len(bannerInfos))
	err = gconv.Structs(bannerInfos, &bannerList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.BannerListRes{
		Code: 200,
		Msg:  "success",
		Data: bannerList,
	}, nil
}

func (*ControllerBanner) BannerClick(ctx context.Context, req *v1.BannerClickReq) (res *v1.BannerClickRes, err error) {

	input := &model.BannerClickInput{
		BannerId: req.BannerId,
		DeviceId: req.DeviceId,
	}

	service.Banner().BannerClick(ctx, input)

	// 这里不管成功失败吧，不用给前端返回错误信息
	return &v1.BannerClickRes{
		Code: 200,
		Msg:  "success",
	}, nil
}
