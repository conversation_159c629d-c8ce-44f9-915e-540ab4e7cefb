package islamic

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/api/pbentity"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"

	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/util/gconv"
)

type Controller struct {
	v1.UnimplementedSurahServiceServer
}

type ControllerNews struct {
	v1.UnimplementedNewsServiceServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterSurahServiceServer(s.Server, &Controller{})
	v1.RegisterNewsServiceServer(s.Server, &ControllerNews{})
	v1.RegisterBannerServiceServer(s.Server, &ControllerBanner{})
}

func (*Controller) SurahList(ctx context.Context, req *v1.SurahListReq) (res *v1.SurahListRes, err error) {
	res = &v1.SurahListRes{}
	res.Code = 200
	res.Msg = "success"
	surahParamInput := &model.SurahParamInput{
		Id:   gconv.Uint(req.Id),
		Name: req.Name,
	}
	surahList := service.Islamic().SurahList(ctx, surahParamInput)

	res.Data = make([]*pbentity.SuratDaftar, 0, len(surahList)) // 初始化切片，预分配空间
	for _, surah := range surahList {
		res.Data = append(res.Data, &pbentity.SuratDaftar{
			Id:          int32(gconv.Uint32(surah.Id)),
			NamaLatin:   surah.NameLatin,
			Nama:        surah.Name,
			JumlahAyat:  int32(surah.JumlahAyat),
			Arti:        surah.Arti,
			Nomor:       int32(surah.Nomor),
			TempatTurun: surah.TempatTurun,
		})
	}
	return res, nil
}

func (*Controller) JuzList(ctx context.Context, req *v1.JuzListReq) (res *v1.JuzListRes, err error) {
	res = &v1.JuzListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.JuzParamInput{
		Name: req.Name,
	}
	juzList := service.Islamic().JuzList(ctx, JuzParamInput)

	Data := make([]*v1.JuzInfo, 0, 30)
	for _, juz := range juzList {
		Data = append(Data, &v1.JuzInfo{
			StartSurahId:   int32(juz.StartSurahId),
			StartSurahName: juz.StartSurahName,
			EndSurahId:     int32(juz.EndSurahId),
			EndSurahName:   juz.EndSurahName,
			StartAyahId:    int32(juz.StartAyahId),
			EndAyahId:      int32(juz.EndAyahId),
			Name:           juz.EndSurahName,
			FirstWord:      juz.EndSurahName,
		})
	}
	res.Data = Data
	return res, nil

}

func (*Controller) AyahList(ctx context.Context, req *v1.AyahListReq) (res *v1.AyahListRes, err error) {

	res = &v1.AyahListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahParamInput{
		Id:      req.Id,
		SurahId: req.SurahId,
		JuzId:   req.JuzId,
		Page:    req.Page,
	}
	juzList := service.Islamic().AyahList(ctx, JuzParamInput)

	res.Data = make([]*pbentity.SuratAyat, 0, len(juzList)) // 初始化切片，预分配空间
	for _, surah := range juzList {
		res.Data = append(res.Data, &pbentity.SuratAyat{
			Id:      int32(surah.Id),
			AyatId:  int32(surah.Id),
			SurahId: int32(surah.SurahId),
			Nomor:   int32(surah.Nomor),
			Ar:      surah.Ar,
			Tr:      surah.Tr,
			Idn:     surah.Idn,
			Page:    int32(surah.Page),
			Juz:     int32(surah.Juz),
		})
	}
	return res, nil

}

//func (*Controller) SurahInfo(ctx context.Context, req *v1.SurahInfoReq) (res *v1.SurahInfoRes, err error) {
//
//	res = &v1.SurahInfoRes{}
//	res.Code = 200
//	res.Msg = "success"
//	res.Data = &pbentity.SuratDaftar{
//		Id:          1,
//		Nama:        "212323",
//		Nomor:       1,
//		NamaLatin:   "Pembukaan",
//		JumlahAyat:  4,
//		TempatTurun: "Pembukaan",
//		Arti:        "Pembukaan",
//		Deskripsi:   "Pembukaan",
//		Audio:       "https://equran.nos.wjv-1.neo.id/audio-full/Misyari-Rasyid-Al-Afasi/012.mp3",
//		Status:      1,
//	}
//	return res, nil
//}
//
//func (*Controller) SurahDesc(ctx context.Context, req *v1.SurahDescReq) (res *v1.SurahDescRes, err error) {
//
//	res = &v1.SurahDescRes{}
//	res.Code = 200
//	res.Msg = "success"
//	res.Data = &pbentity.SuratTafsir{
//		Id:        1,
//		TafsirId:  1,
//		SurahId:   1,
//		AyatNomor: 1,
//		Tafsir:    "Pembukaan",
//	}
//	return res, nil
//}

func (*ControllerNews) NewsCategoryList(ctx context.Context, req *v1.NewsCategoryListReq) (res *v1.NewsCategoryListRes, err error) {

	res = &v1.NewsCategoryListRes{}
	res.Code = 200
	res.Msg = "success"
	Data := make([]*v1.CategoryInfo, 0)
	Data = append(Data, &v1.CategoryInfo{
		CategoryId: 1,
		LanguageId: 1,
		Name:       "Technology",
		Data: &pbentity.NewsCategory{
			Id:        1,
			ParentId:  0,
			Status:    1,
			Sort:      1,
			CoverImgs: "https://example.com/cover.jpg",
		},
	})
	res.Data = Data
	return res, nil
}

func (*ControllerNews) NewsListByCateId(ctx context.Context, req *v1.NewsListByCateIdReq) (res *v1.NewsListByCateIdRes, err error) {

	res = &v1.NewsListByCateIdRes{}
	res.Code = 200
	res.Msg = "success"
	Data := make([]*v1.ArticleInfo, 0)
	Data = append(Data, &v1.ArticleInfo{
		ArticleId:  1,
		LanguageId: 1,
		Name:       "Latest News",
		Content:    "This is the content of the latest news article.",
		Data: &pbentity.NewsArticle{
			Id:          1,
			CategoryId:  1,
			Author:      "Admin",
			CoverImgs:   "https://example.com/news-cover.jpg",
			IsTop:       1,
			IsRecommend: 1,
		},
	})
	res.Data = Data
	return res, nil
}

func (*ControllerNews) NewsTopicList(ctx context.Context, req *v1.NewsTopicListReq) (res *v1.NewsTopicListRes, err error) {

	res = &v1.NewsTopicListRes{}
	res.Code = 200
	res.Msg = "success"
	Data := make([]*v1.TopicInfo, 0)
	Data = append(Data, &v1.TopicInfo{
		TopicId:    1,
		LanguageId: 1,
		Name:       "Latest News",
		ShortName:  "article.",
		Data: &pbentity.NewsTopic{
			Id:        1,
			Sort:      1,
			TopicImgs: "https://example.com/news-cover.jpg",
		},
	})
	res.Data = Data
	return res, nil
}

func (*ControllerNews) NewsListByTopicId(ctx context.Context, req *v1.NewsListByTopicIdReq) (res *v1.NewsListByTopicIdRes, err error) {

	res = &v1.NewsListByTopicIdRes{}
	res.Code = 200
	res.Msg = "success"
	Data := make([]*v1.ArticleInfo, 0)
	Data = append(Data, &v1.ArticleInfo{
		ArticleId:  1,
		LanguageId: 1,
		Name:       "Latest News",
		Content:    "This is the content of the latest news article.",
		Data: &pbentity.NewsArticle{
			Id:          1,
			CategoryId:  1,
			Author:      "Admin",
			CoverImgs:   "https://example.com/news-cover.jpg",
			IsTop:       1,
			IsRecommend: 1,
		},
	})
	res.Data = Data
	return res, nil
}
