package islamic

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type ControllerCalendar struct {
	v1.UnimplementedCalendarServiceServer
}

func (*ControllerCalendar) GetCalendar(ctx context.Context, req *v1.CalendarReq) (res *v1.CalendarRes, err error) {
	// 参数验证
	if req.Year <= 0 || req.Month <= 0 || req.Month > 12 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "invalid year or month")
	}

	// 转换protobuf请求为内部参数
	methodCode := req.MethodCode
	if methodCode == "" || methodCode == "AUTO" {
		methodCode = "UMMUL_QURA"
	}

	input := &model.CalendarGetInput{
		Year:           int32(req.Year),
		Month:          int32(req.Month),
		MethodCode:     methodCode,
		DateAdjustment: req.DateAdjustment,
	}

	// 调用service层获取日历数据
	calendarData, err := service.Calendar().GetCalendar(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 使用gconv.Structs转换数据结构
	calendarList := make([]*v1.CalendarDateInfo, 0, len(calendarData))
	err = gconv.Structs(calendarData, &calendarList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.CalendarRes{
		Code: 200,
		Msg:  "success",
		Data: calendarList,
	}, nil
}
