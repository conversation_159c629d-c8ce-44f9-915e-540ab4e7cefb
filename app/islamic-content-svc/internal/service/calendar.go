// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
)

type (
	ICalendar interface {
		GetCalendar(ctx context.Context, req *v1.CalendarReq) (res *v1.CalendarRes, err error)
	}
)

var (
	localCalendar ICalendar
)

func Calendar() ICalendar {
	if localCalendar == nil {
		panic("implement not found for interface ICalendar, forgot register?")
	}
	return localCalendar
}

func RegisterCalendar(i ICalendar) {
	localCalendar = i
}
