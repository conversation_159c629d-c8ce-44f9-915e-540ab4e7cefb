// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/model"
)

type (
	IIslamic interface {
		SurahList(ctx context.Context, in *model.SurahParamInput) (out []*model.SurahParamOutput)
		JuzList(ctx context.Context, in *model.JuzParamInput) (out []*model.JuzParamOutput)
		AyahList(ctx context.Context, in *model.AyahParamInput) (out []*model.AyahParamOutPut)
	}
)

var (
	localIslamic IIslamic
)

func Islamic() IIslamic {
	if localIslamic == nil {
		panic("implement not found for interface IIslamic, forgot register?")
	}
	return localIslamic
}

func RegisterIslamic(i IIslamic) {
	localIslamic = i
}
