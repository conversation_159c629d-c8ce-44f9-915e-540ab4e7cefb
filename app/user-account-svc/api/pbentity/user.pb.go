// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: pbentity/user.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type User struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                                                                //
	AgentId               uint32                 `protobuf:"varint,2,opt,name=AgentId,proto3" json:"AgentId,omitempty" dc:"上级（代理）的id"`                                                       // 上级（代理）的id
	AgentCode             string                 `protobuf:"bytes,3,opt,name=AgentCode,proto3" json:"AgentCode,omitempty" dc:"注册时填写的代理码（选填）"`                                                // 注册时填写的代理码（选填）
	Account               string                 `protobuf:"bytes,4,opt,name=Account,proto3" json:"Account,omitempty" dc:"账号"`                                                               // 账号
	Password              string                 `protobuf:"bytes,5,opt,name=Password,proto3" json:"Password,omitempty" dc:"密码"`                                                             // 密码
	PayPassword           string                 `protobuf:"bytes,6,opt,name=PayPassword,proto3" json:"PayPassword,omitempty" dc:"支付密码"`                                                     // 支付密码
	PasswordModifyTime    int64                  `protobuf:"varint,7,opt,name=PasswordModifyTime,proto3" json:"PasswordModifyTime,omitempty" dc:"登录密码支付密码最近修改时间"`                            // 登录密码支付密码最近修改时间
	AreaCode              string                 `protobuf:"bytes,8,opt,name=AreaCode,proto3" json:"AreaCode,omitempty" dc:"手机国际区号，如：86"`                                                    // 手机国际区号，如：86
	PhoneNum              string                 `protobuf:"bytes,9,opt,name=PhoneNum,proto3" json:"PhoneNum,omitempty" dc:"手机号"`                                                            // 手机号
	BindPhoneTime         int64                  `protobuf:"varint,10,opt,name=BindPhoneTime,proto3" json:"BindPhoneTime,omitempty" dc:"手机号绑定时间"`                                            // 手机号绑定时间
	Email                 string                 `protobuf:"bytes,11,opt,name=Email,proto3" json:"Email,omitempty" dc:"邮箱地址"`                                                                // 邮箱地址
	BindEmailTime         int64                  `protobuf:"varint,12,opt,name=BindEmailTime,proto3" json:"BindEmailTime,omitempty" dc:"邮箱绑定时间"`                                             // 邮箱绑定时间
	BindRealNameTime      int64                  `protobuf:"varint,13,opt,name=BindRealNameTime,proto3" json:"BindRealNameTime,omitempty" dc:"真实姓名绑定时间"`                                     // 真实姓名绑定时间
	VipLevel              int32                  `protobuf:"varint,14,opt,name=VipLevel,proto3" json:"VipLevel,omitempty" dc:"vip等级"`                                                        // vip等级
	LevelId               uint32                 `protobuf:"varint,15,opt,name=LevelId,proto3" json:"LevelId,omitempty" dc:"会员层级id"`                                                         // 会员层级id
	IsBanned              int32                  `protobuf:"varint,16,opt,name=IsBanned,proto3" json:"IsBanned,omitempty" dc:"账号封号状态： 1 正常 2 封号"`                                            // 账号封号状态： 1 正常 2 封号
	IsProhibit            int32                  `protobuf:"varint,17,opt,name=IsProhibit,proto3" json:"IsProhibit,omitempty" dc:"提取状态：1 正常 2 禁提"`                                           // 提取状态：1 正常 2 禁提
	IsOnline              int32                  `protobuf:"varint,18,opt,name=IsOnline,proto3" json:"IsOnline,omitempty" dc:"是否在线：1是 2 否"`                                                  // 是否在线：1是 2 否
	OnlineDuration        int64                  `protobuf:"varint,19,opt,name=OnlineDuration,proto3" json:"OnlineDuration,omitempty" dc:"在线时长（单位：秒）"`                                       // 在线时长（单位：秒）
	SigninCount           int32                  `protobuf:"varint,20,opt,name=SigninCount,proto3" json:"SigninCount,omitempty" dc:"登录次数"`                                                   // 登录次数
	LastSigninTime        int64                  `protobuf:"varint,21,opt,name=LastSigninTime,proto3" json:"LastSigninTime,omitempty" dc:"最后一次登录时间"`                                         // 最后一次登录时间
	LastSigninIp          string                 `protobuf:"bytes,22,opt,name=LastSigninIp,proto3" json:"LastSigninIp,omitempty" dc:"最后登录ip"`                                                // 最后登录ip
	LastSigninDeviceId    string                 `protobuf:"bytes,23,opt,name=LastSigninDeviceId,proto3" json:"LastSigninDeviceId,omitempty" dc:"最后登录设备号"`                                   // 最后登录设备号
	LastSigninAppType     int32                  `protobuf:"varint,24,opt,name=LastSigninAppType,proto3" json:"LastSigninAppType,omitempty" dc:"最近登录应用类型（1:android 2: ios，3:h5，4:web，5:其他）"` // 最近登录应用类型（1:android 2: ios，3:h5，4:web，5:其他）
	LastSigninAppVersion  string                 `protobuf:"bytes,25,opt,name=LastSigninAppVersion,proto3" json:"LastSigninAppVersion,omitempty" dc:"最近登录应用类型版本号"`                           // 最近登录应用类型版本号
	SignupIp              string                 `protobuf:"bytes,26,opt,name=SignupIp,proto3" json:"SignupIp,omitempty" dc:"注册ip"`                                                          // 注册ip
	SignupIpRegion        string                 `protobuf:"bytes,27,opt,name=SignupIpRegion,proto3" json:"SignupIpRegion,omitempty" dc:"注册IP地理区域"`                                          // 注册IP地理区域
	SignupDeviceId        string                 `protobuf:"bytes,28,opt,name=SignupDeviceId,proto3" json:"SignupDeviceId,omitempty" dc:"注册设备号（设备指纹）"`                                       // 注册设备号（设备指纹）
	SignupDeviceOs        string                 `protobuf:"bytes,29,opt,name=SignupDeviceOs,proto3" json:"SignupDeviceOs,omitempty" dc:"注册设备系统（android,ios,windows,mac,...）"`               // 注册设备系统（android,ios,windows,mac,...）
	SignupDeviceOsVersion string                 `protobuf:"bytes,30,opt,name=SignupDeviceOsVersion,proto3" json:"SignupDeviceOsVersion,omitempty" dc:"注册设备系统版本号"`                           // 注册设备系统版本号
	SignupDeviceType      int32                  `protobuf:"varint,31,opt,name=SignupDeviceType,proto3" json:"SignupDeviceType,omitempty" dc:"注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）"` // 注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）
	SignupAppType         int32                  `protobuf:"varint,32,opt,name=SignupAppType,proto3" json:"SignupAppType,omitempty" dc:"应用类型（1:android 2: ios，3:h5，4:web，5:其他）"`             // 应用类型（1:android 2: ios，3:h5，4:web，5:其他）
	SignupAppVersion      string                 `protobuf:"bytes,33,opt,name=SignupAppVersion,proto3" json:"SignupAppVersion,omitempty" dc:"注册应用类型版本号"`                                     // 注册应用类型版本号
	SignupHost            string                 `protobuf:"bytes,34,opt,name=SignupHost,proto3" json:"SignupHost,omitempty" dc:"注册域名(接口域名)"`                                                // 注册域名(接口域名)
	SignupDomain          string                 `protobuf:"bytes,35,opt,name=SignupDomain,proto3" json:"SignupDomain,omitempty" dc:"注册域名(页面原始域名)"`                                          // 注册域名(页面原始域名)
	SignupDomain2         string                 `protobuf:"bytes,36,opt,name=SignupDomain2,proto3" json:"SignupDomain2,omitempty" dc:"注册域名(页面域名)"`                                          // 注册域名(页面域名)
	LastSigninLogId       int32                  `protobuf:"varint,37,opt,name=LastSigninLogId,proto3" json:"LastSigninLogId,omitempty" dc:"最后一次登录日志id（如果有分表的话，要考虑时间）"`                      // 最后一次登录日志id（如果有分表的话，要考虑时间）
	DeviceTokenIos        string                 `protobuf:"bytes,38,opt,name=DeviceTokenIos,proto3" json:"DeviceTokenIos,omitempty" dc:"IOS推送token"`                                        // IOS推送token
	DeviceTokenAndroid    string                 `protobuf:"bytes,39,opt,name=DeviceTokenAndroid,proto3" json:"DeviceTokenAndroid,omitempty" dc:"android推送token(FCM)"`                       // android推送token(FCM)
	SecurityPassword      string                 `protobuf:"bytes,40,opt,name=SecurityPassword,proto3" json:"SecurityPassword,omitempty" dc:"安全密码，修改个人绑定信息时要验证"`                             // 安全密码，修改个人绑定信息时要验证
	Version               int32                  `protobuf:"varint,41,opt,name=Version,proto3" json:"Version,omitempty" dc:"该记录的版本号"`                                                        // 该记录的版本号
	IsTest                int32                  `protobuf:"varint,42,opt,name=IsTest,proto3" json:"IsTest,omitempty" dc:"测试账号： 1 是 ，其他值：否"`                                                 // 测试账号： 1 是 ，其他值：否
	LimitStartTime        int64                  `protobuf:"varint,43,opt,name=LimitStartTime,proto3" json:"LimitStartTime,omitempty" dc:"限制登录开始时间"`                                         // 限制登录开始时间
	LimitEndTime          int64                  `protobuf:"varint,44,opt,name=LimitEndTime,proto3" json:"LimitEndTime,omitempty" dc:"限制登录结束时间"`                                             // 限制登录结束时间
	CreateTime            int64                  `protobuf:"varint,45,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（注册时间）"`                                               // 创建时间（注册时间）
	UpdateTime            int64                  `protobuf:"varint,46,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）"`                          // 更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）
	CreateAccount         string                 `protobuf:"bytes,47,opt,name=CreateAccount,proto3" json:"CreateAccount,omitempty" dc:"创建者账号"`                                               // 创建者账号
	CreateType            int32                  `protobuf:"varint,48,opt,name=CreateType,proto3" json:"CreateType,omitempty" dc:"创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）"`            // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount         string                 `protobuf:"bytes,49,opt,name=UpdateAccount,proto3" json:"UpdateAccount,omitempty" dc:"更新者账号"`                                               // 更新者账号
	UpdateType            int32                  `protobuf:"varint,50,opt,name=UpdateType,proto3" json:"UpdateType,omitempty" dc:"更新者来源"`                                                    // 更新者来源
	InviteCode            string                 `protobuf:"bytes,51,opt,name=InviteCode,proto3" json:"InviteCode,omitempty" dc:"邀请码"`                                                       // 邀请码
	TransferCode          string                 `protobuf:"bytes,52,opt,name=TransferCode,proto3" json:"TransferCode,omitempty" dc:"转线码"`                                                   // 转线码
	NoobTaskFinishTime    int64                  `protobuf:"varint,53,opt,name=NoobTaskFinishTime,proto3" json:"NoobTaskFinishTime,omitempty" dc:"新手任务完成时间"`                                 // 新手任务完成时间
	DataType              int32                  `protobuf:"varint,54,opt,name=DataType,proto3" json:"DataType,omitempty" dc:"数据类型:1正式数据;2测试数据"`                                             // 数据类型:1正式数据;2测试数据
	PixelId               string                 `protobuf:"bytes,55,opt,name=PixelId,proto3" json:"PixelId,omitempty" dc:"像素id"`                                                            // 像素id
	Source                int32                  `protobuf:"varint,56,opt,name=Source,proto3" json:"Source,omitempty" dc:"注册来源( 1直客，2代理，3邀请，4后台）"`                                           // 注册来源( 1直客，2代理，3邀请，4后台）
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_pbentity_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_pbentity_user_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetAgentId() uint32 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *User) GetAgentCode() string {
	if x != nil {
		return x.AgentCode
	}
	return ""
}

func (x *User) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *User) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *User) GetPayPassword() string {
	if x != nil {
		return x.PayPassword
	}
	return ""
}

func (x *User) GetPasswordModifyTime() int64 {
	if x != nil {
		return x.PasswordModifyTime
	}
	return 0
}

func (x *User) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *User) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *User) GetBindPhoneTime() int64 {
	if x != nil {
		return x.BindPhoneTime
	}
	return 0
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetBindEmailTime() int64 {
	if x != nil {
		return x.BindEmailTime
	}
	return 0
}

func (x *User) GetBindRealNameTime() int64 {
	if x != nil {
		return x.BindRealNameTime
	}
	return 0
}

func (x *User) GetVipLevel() int32 {
	if x != nil {
		return x.VipLevel
	}
	return 0
}

func (x *User) GetLevelId() uint32 {
	if x != nil {
		return x.LevelId
	}
	return 0
}

func (x *User) GetIsBanned() int32 {
	if x != nil {
		return x.IsBanned
	}
	return 0
}

func (x *User) GetIsProhibit() int32 {
	if x != nil {
		return x.IsProhibit
	}
	return 0
}

func (x *User) GetIsOnline() int32 {
	if x != nil {
		return x.IsOnline
	}
	return 0
}

func (x *User) GetOnlineDuration() int64 {
	if x != nil {
		return x.OnlineDuration
	}
	return 0
}

func (x *User) GetSigninCount() int32 {
	if x != nil {
		return x.SigninCount
	}
	return 0
}

func (x *User) GetLastSigninTime() int64 {
	if x != nil {
		return x.LastSigninTime
	}
	return 0
}

func (x *User) GetLastSigninIp() string {
	if x != nil {
		return x.LastSigninIp
	}
	return ""
}

func (x *User) GetLastSigninDeviceId() string {
	if x != nil {
		return x.LastSigninDeviceId
	}
	return ""
}

func (x *User) GetLastSigninAppType() int32 {
	if x != nil {
		return x.LastSigninAppType
	}
	return 0
}

func (x *User) GetLastSigninAppVersion() string {
	if x != nil {
		return x.LastSigninAppVersion
	}
	return ""
}

func (x *User) GetSignupIp() string {
	if x != nil {
		return x.SignupIp
	}
	return ""
}

func (x *User) GetSignupIpRegion() string {
	if x != nil {
		return x.SignupIpRegion
	}
	return ""
}

func (x *User) GetSignupDeviceId() string {
	if x != nil {
		return x.SignupDeviceId
	}
	return ""
}

func (x *User) GetSignupDeviceOs() string {
	if x != nil {
		return x.SignupDeviceOs
	}
	return ""
}

func (x *User) GetSignupDeviceOsVersion() string {
	if x != nil {
		return x.SignupDeviceOsVersion
	}
	return ""
}

func (x *User) GetSignupDeviceType() int32 {
	if x != nil {
		return x.SignupDeviceType
	}
	return 0
}

func (x *User) GetSignupAppType() int32 {
	if x != nil {
		return x.SignupAppType
	}
	return 0
}

func (x *User) GetSignupAppVersion() string {
	if x != nil {
		return x.SignupAppVersion
	}
	return ""
}

func (x *User) GetSignupHost() string {
	if x != nil {
		return x.SignupHost
	}
	return ""
}

func (x *User) GetSignupDomain() string {
	if x != nil {
		return x.SignupDomain
	}
	return ""
}

func (x *User) GetSignupDomain2() string {
	if x != nil {
		return x.SignupDomain2
	}
	return ""
}

func (x *User) GetLastSigninLogId() int32 {
	if x != nil {
		return x.LastSigninLogId
	}
	return 0
}

func (x *User) GetDeviceTokenIos() string {
	if x != nil {
		return x.DeviceTokenIos
	}
	return ""
}

func (x *User) GetDeviceTokenAndroid() string {
	if x != nil {
		return x.DeviceTokenAndroid
	}
	return ""
}

func (x *User) GetSecurityPassword() string {
	if x != nil {
		return x.SecurityPassword
	}
	return ""
}

func (x *User) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *User) GetIsTest() int32 {
	if x != nil {
		return x.IsTest
	}
	return 0
}

func (x *User) GetLimitStartTime() int64 {
	if x != nil {
		return x.LimitStartTime
	}
	return 0
}

func (x *User) GetLimitEndTime() int64 {
	if x != nil {
		return x.LimitEndTime
	}
	return 0
}

func (x *User) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *User) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *User) GetCreateAccount() string {
	if x != nil {
		return x.CreateAccount
	}
	return ""
}

func (x *User) GetCreateType() int32 {
	if x != nil {
		return x.CreateType
	}
	return 0
}

func (x *User) GetUpdateAccount() string {
	if x != nil {
		return x.UpdateAccount
	}
	return ""
}

func (x *User) GetUpdateType() int32 {
	if x != nil {
		return x.UpdateType
	}
	return 0
}

func (x *User) GetInviteCode() string {
	if x != nil {
		return x.InviteCode
	}
	return ""
}

func (x *User) GetTransferCode() string {
	if x != nil {
		return x.TransferCode
	}
	return ""
}

func (x *User) GetNoobTaskFinishTime() int64 {
	if x != nil {
		return x.NoobTaskFinishTime
	}
	return 0
}

func (x *User) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *User) GetPixelId() string {
	if x != nil {
		return x.PixelId
	}
	return ""
}

func (x *User) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

var File_pbentity_user_proto protoreflect.FileDescriptor

const file_pbentity_user_proto_rawDesc = "" +
	"\n" +
	"\x13pbentity/user.proto\x12\bpbentity\"\xbe\x0f\n" +
	"\x04User\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x18\n" +
	"\aAgentId\x18\x02 \x01(\rR\aAgentId\x12\x1c\n" +
	"\tAgentCode\x18\x03 \x01(\tR\tAgentCode\x12\x18\n" +
	"\aAccount\x18\x04 \x01(\tR\aAccount\x12\x1a\n" +
	"\bPassword\x18\x05 \x01(\tR\bPassword\x12 \n" +
	"\vPayPassword\x18\x06 \x01(\tR\vPayPassword\x12.\n" +
	"\x12PasswordModifyTime\x18\a \x01(\x03R\x12PasswordModifyTime\x12\x1a\n" +
	"\bAreaCode\x18\b \x01(\tR\bAreaCode\x12\x1a\n" +
	"\bPhoneNum\x18\t \x01(\tR\bPhoneNum\x12$\n" +
	"\rBindPhoneTime\x18\n" +
	" \x01(\x03R\rBindPhoneTime\x12\x14\n" +
	"\x05Email\x18\v \x01(\tR\x05Email\x12$\n" +
	"\rBindEmailTime\x18\f \x01(\x03R\rBindEmailTime\x12*\n" +
	"\x10BindRealNameTime\x18\r \x01(\x03R\x10BindRealNameTime\x12\x1a\n" +
	"\bVipLevel\x18\x0e \x01(\x05R\bVipLevel\x12\x18\n" +
	"\aLevelId\x18\x0f \x01(\rR\aLevelId\x12\x1a\n" +
	"\bIsBanned\x18\x10 \x01(\x05R\bIsBanned\x12\x1e\n" +
	"\n" +
	"IsProhibit\x18\x11 \x01(\x05R\n" +
	"IsProhibit\x12\x1a\n" +
	"\bIsOnline\x18\x12 \x01(\x05R\bIsOnline\x12&\n" +
	"\x0eOnlineDuration\x18\x13 \x01(\x03R\x0eOnlineDuration\x12 \n" +
	"\vSigninCount\x18\x14 \x01(\x05R\vSigninCount\x12&\n" +
	"\x0eLastSigninTime\x18\x15 \x01(\x03R\x0eLastSigninTime\x12\"\n" +
	"\fLastSigninIp\x18\x16 \x01(\tR\fLastSigninIp\x12.\n" +
	"\x12LastSigninDeviceId\x18\x17 \x01(\tR\x12LastSigninDeviceId\x12,\n" +
	"\x11LastSigninAppType\x18\x18 \x01(\x05R\x11LastSigninAppType\x122\n" +
	"\x14LastSigninAppVersion\x18\x19 \x01(\tR\x14LastSigninAppVersion\x12\x1a\n" +
	"\bSignupIp\x18\x1a \x01(\tR\bSignupIp\x12&\n" +
	"\x0eSignupIpRegion\x18\x1b \x01(\tR\x0eSignupIpRegion\x12&\n" +
	"\x0eSignupDeviceId\x18\x1c \x01(\tR\x0eSignupDeviceId\x12&\n" +
	"\x0eSignupDeviceOs\x18\x1d \x01(\tR\x0eSignupDeviceOs\x124\n" +
	"\x15SignupDeviceOsVersion\x18\x1e \x01(\tR\x15SignupDeviceOsVersion\x12*\n" +
	"\x10SignupDeviceType\x18\x1f \x01(\x05R\x10SignupDeviceType\x12$\n" +
	"\rSignupAppType\x18  \x01(\x05R\rSignupAppType\x12*\n" +
	"\x10SignupAppVersion\x18! \x01(\tR\x10SignupAppVersion\x12\x1e\n" +
	"\n" +
	"SignupHost\x18\" \x01(\tR\n" +
	"SignupHost\x12\"\n" +
	"\fSignupDomain\x18# \x01(\tR\fSignupDomain\x12$\n" +
	"\rSignupDomain2\x18$ \x01(\tR\rSignupDomain2\x12(\n" +
	"\x0fLastSigninLogId\x18% \x01(\x05R\x0fLastSigninLogId\x12&\n" +
	"\x0eDeviceTokenIos\x18& \x01(\tR\x0eDeviceTokenIos\x12.\n" +
	"\x12DeviceTokenAndroid\x18' \x01(\tR\x12DeviceTokenAndroid\x12*\n" +
	"\x10SecurityPassword\x18( \x01(\tR\x10SecurityPassword\x12\x18\n" +
	"\aVersion\x18) \x01(\x05R\aVersion\x12\x16\n" +
	"\x06IsTest\x18* \x01(\x05R\x06IsTest\x12&\n" +
	"\x0eLimitStartTime\x18+ \x01(\x03R\x0eLimitStartTime\x12\"\n" +
	"\fLimitEndTime\x18, \x01(\x03R\fLimitEndTime\x12\x1e\n" +
	"\n" +
	"CreateTime\x18- \x01(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18. \x01(\x03R\n" +
	"UpdateTime\x12$\n" +
	"\rCreateAccount\x18/ \x01(\tR\rCreateAccount\x12\x1e\n" +
	"\n" +
	"CreateType\x180 \x01(\x05R\n" +
	"CreateType\x12$\n" +
	"\rUpdateAccount\x181 \x01(\tR\rUpdateAccount\x12\x1e\n" +
	"\n" +
	"UpdateType\x182 \x01(\x05R\n" +
	"UpdateType\x12\x1e\n" +
	"\n" +
	"InviteCode\x183 \x01(\tR\n" +
	"InviteCode\x12\"\n" +
	"\fTransferCode\x184 \x01(\tR\fTransferCode\x12.\n" +
	"\x12NoobTaskFinishTime\x185 \x01(\x03R\x12NoobTaskFinishTime\x12\x1a\n" +
	"\bDataType\x186 \x01(\x05R\bDataType\x12\x18\n" +
	"\aPixelId\x187 \x01(\tR\aPixelId\x12\x16\n" +
	"\x06Source\x188 \x01(\x05R\x06SourceB-Z+halalplus/app/user-account-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_user_proto_rawDescOnce sync.Once
	file_pbentity_user_proto_rawDescData []byte
)

func file_pbentity_user_proto_rawDescGZIP() []byte {
	file_pbentity_user_proto_rawDescOnce.Do(func() {
		file_pbentity_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_user_proto_rawDesc), len(file_pbentity_user_proto_rawDesc)))
	})
	return file_pbentity_user_proto_rawDescData
}

var file_pbentity_user_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_user_proto_goTypes = []any{
	(*User)(nil), // 0: pbentity.User
}
var file_pbentity_user_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_user_proto_init() }
func file_pbentity_user_proto_init() {
	if File_pbentity_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_user_proto_rawDesc), len(file_pbentity_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_user_proto_goTypes,
		DependencyIndexes: file_pbentity_user_proto_depIdxs,
		MessageInfos:      file_pbentity_user_proto_msgTypes,
	}.Build()
	File_pbentity_user_proto = out.File
	file_pbentity_user_proto_goTypes = nil
	file_pbentity_user_proto_depIdxs = nil
}
