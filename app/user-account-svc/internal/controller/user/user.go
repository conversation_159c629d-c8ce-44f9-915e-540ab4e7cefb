package user

import (
	"context"
	v1 "halalplus/app/user-account-svc/api/user/v1"
	"halalplus/app/user-account-svc/internal/consts"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/service"

	"github.com/gogf/gf/v2/util/gconv"

	"github.com/gogf/gf/v2/frame/g"

	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
)

type Controller struct {
	v1.UnimplementedUserServiceServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterUserServiceServer(s.Server, &Controller{})
}

func (*Controller) SignUp(ctx context.Context, req *v1.SignUpReq) (res *v1.SignUpRes, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) SignIn(ctx context.Context, req *v1.SignInReq) (res *v1.UserSignInRes, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) SignInByAccount(ctx context.Context, req *v1.SignInByAccountReq) (res *v1.SignInByAccountRes, err error) {
	//  grpcurl -plaintext -d '{}' 127.0.0.1:9200 user.v1.UserService/SignInByAccount
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) SignInByPhone(ctx context.Context, req *v1.SignInByPhoneReq) (res *v1.SignInByPhoneRes, err error) {
	// 示例调试命令：
	// grpcurl -plaintext -d '{"opt_code":"123456","phone_info":{"phone_num":"************","area_code":"+62"}}' 127.0.0.1:9200 user.v1.UserService/SignInByPhone

	g.Log().Line().Debug(ctx, req)

	// TODO：检查手机号码有效性
	// TODO： 设备号+手机号，风控检查

	// 验证手机号验证码 是否匹配
	if req.OptCode != "123456" {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "验证码无效")
	}

	frontInfoModel := &model.FrontInfo{}
	gconv.Scan(req.FrontInfo, frontInfoModel)
	userModel, token, err := service.User().SignInByPhoneNum(ctx, req.PhoneInfo.AreaCode, req.PhoneInfo.PhoneNum, frontInfoModel)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	g.Log().Debug(ctx, userModel)
	res = &v1.SignInByPhoneRes{
		Code: 200,
		Msg:  "登录成功",
		Data: &v1.SignInByPhoneResData{
			Token:     token,
			SessionId: userModel.SessionId,
			Secret:    userModel.SecretKey,
			UserInfo: &v1.UserInfo{
				Id:           userModel.Id,
				BindPhone:    true,
				BindRealName: false, // 实名认证
				Gender:       v1.Gender(gconv.Int32(userModel.Gender)),
				Avatar:       userModel.Avatar,
				Nickname:     userModel.Nickname,
				PhoneNum:     userModel.PhoneNum,
				AreaCode:     userModel.AreaCode,
			},
		},
	}

	return res, nil
}

func (*Controller) PhoneValidCheck(ctx context.Context, req *v1.PhoneValidCheckReq) (res *v1.PhoneValidCheckRes, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) RefreshToken(ctx context.Context, req *v1.RefreshTokenReq) (res *v1.RefreshTokenRes, err error) {
	// get header
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	res = &v1.RefreshTokenRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.RefreshTokenResData{},
	}

	if headerMap.Contains(consts.HttpSessionId) {
		sessionId := gconv.String(headerMap.Get(consts.HttpSessionId))
		frontInfo := &model.FrontInfo{}
		gconv.Struct(req.FrontInfo, frontInfo)
		token, err := service.Session().RefreshJwtToken(ctx, sessionId, frontInfo)
		if err != nil {
			g.Log().Error(ctx, err)
		}
		res.Data.Token = token
	}

	return res, nil
}

func (*Controller) UserInfo(ctx context.Context, req *v1.UserInfoReq) (res *v1.UserInfoRes, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	userInfoModel, err := service.User().GetUserInfo(ctx, uid)
	userInfo := &v1.UserInfo{}
	gconv.Struct(userInfoModel, userInfo)
	res = &v1.UserInfoRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UserInfoResData{
			UserInfo: userInfo,
		},
	}
	return res, nil
}

func (*Controller) UpdateUserInfo(ctx context.Context, req *v1.UpdateUserInfoReq) (res *v1.UpdateUserInfoRes, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	updatedAttr := do.UserInfo{}
	if !g.IsEmpty(req.Nickname) {
		updatedAttr.Nickname = req.Nickname
	}
	if !g.IsEmpty(req.Avatar) {
		updatedAttr.Avatar = req.Avatar
	}
	if !g.IsEmpty(req.Gender) {
		updatedAttr.Gender = req.Gender
	}
	_, err = dao.UserInfo.Ctx(ctx).Where(dao.UserInfo.Columns().UserId, uid).Update(updatedAttr)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	return &v1.UpdateUserInfoRes{
		Code: 200,
		Msg:  "success",
	}, nil
}

func (*Controller) SendVerifyCode(ctx context.Context, req *v1.SendVerifyCodeReq) (res *v1.SendVerifyCodeRes, err error) {
	g.Log().Line().Debug(ctx, req)
	// TODO:  发送验证码
	res = &v1.SendVerifyCodeRes{
		Code: 200,
		Msg:  "success",
	}
	return res, nil
}
