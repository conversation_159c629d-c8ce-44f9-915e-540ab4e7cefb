syntax = "proto3";

package common;
import "google/protobuf/any.proto";
option go_package = "halalplus/api/common;common";

message RequestHeader {
  string request_id = 1;    // 请求唯一ID.
  int64  timestamp  = 2;    // 毫秒时间戳
  string trace_id   = 3;    // 链路追踪ID
  string client_ip  = 4;    // 客户端IP
  string lang       = 5;    // 客户端语言
}

message PageRequest {
  int32 page = 1;  // 当前页
  int32 size = 2;  // 每页数量
}

message PageResponse {
  int32 page = 1;   // 当前页
  int32 size = 2;   // 每页数量
  int32 total = 3;  // 总条数
}

message Error {
  int32 code    = 1; // 错误编码，扩展gcode
  string reason = 2; // 错误原因
  string detail = 3; // 可选详细信息
}

// CommonResponse 是所有接口统一返回结构
// 包含：
// - code：业务状态码，0 表示成功
// - msg：人类可读的错误或提示信息
// - error: 错误详情
// - data：具体业务数据，根据接口不同可能有不同结构
message CommonResponse {
  int32 code = 1; // 业务码
  string msg = 2; // 错误原因
  Error error = 3; // 错误详情
  google.protobuf.Any data = 4;  // 业务数据
}